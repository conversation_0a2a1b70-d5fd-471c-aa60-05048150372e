// Web API配置文件，定义应用中所有的API路径

export const API_PREFIX = '/api';

// Session分析API接口类型
export interface SessionApiEndpoints {
  GET_CONDITION_CONFIG: string;
  GET_CONDITION_SCENE: string;
  GET_CONDITION_TASK_VERSIONS: string;
  GET_CONDITION_TASK_NODES: string;
  POST_SESSION_PAGE: string;
  GET_SESSION_DETAIL: string;
  GET_SESSION_MESSAGES: string;
  GET_SESSION_EXEC_PATH: string;
  GET_SESSION_SIGNALS: string;
  FAVORITE_SESSION: string;
  UNFAVORITE_SESSION: string;
  GET_FAVORITE_SESSIONS: string;
  SAVE_FILTER: string;
  GET_SAVED_FILTERS: string;
  DELETE_SAVED_FILTER: string;
  LIST_SAVED_FILTER_RECORDS: string;
  ADD_SAVED_FILTER_RECORD: string;
  DELETE_SAVED_FILTER_RECORD: string;
}

// Session分析API
export const SESSION_API: SessionApiEndpoints = {
  // 获取会话筛选条件配置
  GET_CONDITION_CONFIG: `${API_PREFIX}/aigc/eval/workbench/session/condition/config`,
  // 获取会话场景关系配置
  GET_CONDITION_SCENE: `${API_PREFIX}/aigc/eval/workbench/session/condition/scene`,
  // 获取会话任务版本
  GET_CONDITION_TASK_VERSIONS: `${API_PREFIX}/aigc/eval/workbench/session/condition/task/versions`,
  // 获取会话任务节点
  GET_CONDITION_TASK_NODES: `${API_PREFIX}/aigc/eval/workbench/session/condition/task/nodes`,
  // 获取会话分页列表
  POST_SESSION_PAGE: `${API_PREFIX}/aigc/eval/workbench/session/list`,
  // 获取会话详情 (id参数需要在调用时追加)
  GET_SESSION_DETAIL: `${API_PREFIX}/aigc/eval/workbench/session/`,
  // 获取会话消息 (id参数需要在调用时追加)
  GET_SESSION_MESSAGES: `${API_PREFIX}/aigc/eval/workbench/session/{id}/messages`,
  // 获取会话执行路径 (id参数需要在调用时追加)
  GET_SESSION_EXEC_PATH: `${API_PREFIX}/aigc/eval/workbench/session/{id}/exec-path`,
  // 获取会话信号信息 (id参数需要在调用时追加)
  GET_SESSION_SIGNALS: `${API_PREFIX}/aigc/eval/workbench/session/{id}/signals`,
  // 收藏会话 (id参数需要在调用时追加)
  FAVORITE_SESSION: `${API_PREFIX}/aigc/eval/workbench/session/{id}/favorite`,
  // 取消收藏会话 (id参数需要在调用时追加)
  UNFAVORITE_SESSION: `${API_PREFIX}/aigc/eval/workbench/session/{id}/unfavorite`,
  // 获取收藏的会话列表
  GET_FAVORITE_SESSIONS: `${API_PREFIX}/aigc/eval/workbench/session/favorites`,
  // 保存筛选条件
  SAVE_FILTER: `${API_PREFIX}/aigc/eval/workbench/session/filter/save`,
  // 获取保存的筛选条件列表
  GET_SAVED_FILTERS: `${API_PREFIX}/aigc/eval/workbench/session/filter/list`,
  // 删除已保存的筛选条件 (id参数需要在调用时追加)
  DELETE_SAVED_FILTER: `${API_PREFIX}/aigc/eval/workbench/session/filter/{id}`,
  // New APIs for saved filters as per migration document
  LIST_SAVED_FILTER_RECORDS: `${API_PREFIX}/aigc/eval/workbench/session/condition/record/list`,
  ADD_SAVED_FILTER_RECORD: `${API_PREFIX}/aigc/eval/workbench/session/condition/record/add`,
  DELETE_SAVED_FILTER_RECORD: `${API_PREFIX}/aigc/eval/workbench/session/condition/record/delete`
}; 