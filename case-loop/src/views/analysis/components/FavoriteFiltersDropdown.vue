<template>
  <div class="favorite-filters-dropdown">
    <a-dropdown :trigger="['click']">
      <a-button class="custom-button">
        <template #icon><StarOutlined /></template>
        常用筛选
      </a-button>
      <template #overlay>
        <a-menu>
          <div v-if="favorites.length === 0" class="empty-favorites">
            <a-empty 
              description="暂无常用筛选" 
              :image="Empty.PRESENTED_IMAGE_SIMPLE" 
              :image-style="{ height: '40px' }" 
            />
          </div>
          <template v-else>
            <a-menu-item v-for="filter in favorites" :key="filter.id" @click="onSelect(filter)">
              <div class="favorite-item">
                <span class="favorite-name">{{ filter.name }}</span>
                <a-button 
                  type="text" 
                  size="small" 
                  @click.stop="onRemove(filter)"
                  class="remove-btn"
                >
                  <template #icon><DeleteOutlined /></template>
                </a-button>
              </div>
            </a-menu-item>
          </template>
        </a-menu>
      </template>
    </a-dropdown>
  </div>
</template>

<script>
import { defineComponent } from 'vue';
import { message } from 'ant-design-vue';
import { StarOutlined, DeleteOutlined, SettingOutlined } from '@ant-design/icons-vue';
import { Empty } from 'ant-design-vue';

export default defineComponent({
  name: 'FavoriteFiltersDropdown',
  components: {
    StarOutlined,
    DeleteOutlined,
    SettingOutlined
  },
  props: {
    favorites: {
      type: Array,
      default: () => []
    }
  },
  emits: ['select', 'remove', 'manage'],
  setup(props, { emit }) {
    // 选择筛选条件
    const onSelect = (filter) => {
      emit('select', filter);
    };
    
    // 移除筛选条件
    const onRemove = (filter) => {
      emit('remove', filter);
    };
    
    // 管理收藏
    const onManage = () => {
      emit('manage');
    };
    
    return {
      onSelect,
      onRemove,
      onManage,
      Empty
    };
  }
});
</script>

<style scoped>
.favorite-filters-dropdown {
  display: inline-block;
}

.empty-favorites {
  padding: 16px;
  text-align: center;
  min-width: 200px;
}

.favorite-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.favorite-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.remove-btn {
  opacity: 0.6;
  transition: opacity 0.3s;
}

.remove-btn:hover {
  opacity: 1;
  color: #ff4d4f;
}

.manage-item {
  display: flex;
  align-items: center;
}

.manage-item .anticon {
  margin-right: 8px;
}

.custom-button {
  border-radius: 8px;
  transition: all 0.3s;
}

.custom-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
</style> 