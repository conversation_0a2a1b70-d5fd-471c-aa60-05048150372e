<template>
  <div class="app">
    <a-layout style="min-height: 100vh">
      <!-- 顶部导航 -->
      <a-layout-header class="header">
        <div class="logo-container">
          <div class="logo">AI搭系统</div>
        </div>
        <a-menu
            v-model:selectedKeys="selectedKeys"
            mode="horizontal"
            class="top-menu"
        >
          <a-menu-item v-for="menu in menuConfig" :key="menu.key" @click="handleMainMenuClick(menu)">
            {{ menu.title }}
          </a-menu-item>
        </a-menu>
      </a-layout-header>

      <a-layout>
        <!-- 左侧侧边栏 -->
        <a-layout-sider
            v-model:collapsed="collapsed"
            collapsible
            class="sidebar"
            theme="light"
        >
          <a-menu
              v-model:selectedKeys="sideSelectedKeys"
              v-model:openKeys="openKeys"
              mode="inline"
              theme="light"
          >
            <!-- 根据当前选中的一级菜单动态显示对应的二级菜单 -->
            <template v-if="currentSubmenu && currentSubmenu.children">
              <template v-for="item in currentSubmenu.children" :key="item.key">
                <!-- 如果有子菜单，渲染为SubMenu -->
                <a-sub-menu v-if="item.children && item.children.length" :key="'sub-'+item.key">
                  <template #icon>
                    <component :is="item.icon"/>
                  </template>
                  <template #title>{{ item.title }}</template>
                  <a-menu-item v-for="child in item.children" :key="child.key" @click="handleSubMenuClick(child.key)">
                    {{ child.title }}
                  </a-menu-item>
                </a-sub-menu>
                <!-- 没有子菜单，渲染为MenuItem -->
                <a-menu-item v-else :key="'item-'+item.key" @click="handleSubMenuClick(item.key)">
                  <template #icon>
                    <component :is="item.icon"/>
                  </template>
                  <span>{{ item.title }}</span>
                </a-menu-item>
              </template>
            </template>
          </a-menu>
        </a-layout-sider>

        <!-- 主要内容区 -->
        <a-layout-content class="content">
          <div class="content-container">
            <!-- 使用router-view替代component动态组件 -->
            <router-view></router-view>
            <!-- 仅在路由未匹配时显示欢迎页面 -->
            <template v-if="$route.path === '/'">
              <h1>欢迎使用AI搭系统</h1>
              <p>智能客服大模型应用平台</p>

              <div class="dashboard-cards">
                <a-row :gutter="[16, 16]">
                  <a-col :span="8" v-for="(card, index) in dashboardCards" :key="index">
                    <a-card class="dashboard-card">
                      <template #title>
                        <a-space>
                          <component :is="card.icon"/>
                          <span>{{ card.title }}</span>
                        </a-space>
                      </template>
                      <h2>{{ card.value }}</h2>
                      <p>{{ card.description }}</p>
                    </a-card>
                  </a-col>
                </a-row>
              </div>
            </template>
          </div>
        </a-layout-content>
      </a-layout>
    </a-layout>
  </div>
</template>

<script lang="ts" setup>
import {computed, ref} from 'vue'
import SessionDetail from './views/analysis/SessionDetail.vue'
import {useRouter} from 'vue-router'

// 菜单配置
const menuConfig = [
  {
    key: 'dataLabeling',
    title: '数据标注',
    children: [
      {
        key: 'dashboard',
        title: '仪表盘',
        icon: 'DashboardOutlined'
      },
      {
        key: 'dataManagement',
        title: '数据管理',
        icon: 'DatabaseOutlined',
        children: [
          {key: 'dataRules', title: '数据埋点规则'},
          {key: 'dataCollection', title: '数据获取'}
        ]
      },
      {
        key: 'labelManagement',
        title: '标注管理',
        icon: 'TagOutlined',
        children: [
          {key: 'taskManagement', title: '任务管理'},
          {key: 'dataLabeling', title: '数据标注'},
          {key: 'qualityCheck', title: '标注质检'}
        ]
      },
      {
        key: 'labelManagement',
        title: '打标任务',
        icon: 'TagOutlined',
        children: [
          {key: 'labelTaskDetail', title: '任务详情'}
        ]
      }
    ]
  },
  {
    key: 'evaluationManagement',
    title: '评测管理',
    children: [
      {
        key: 'evalDashboard',
        title: '评测概览',
        icon: 'DashboardOutlined'
      },
      {
        key: 'modelEvaluation',
        title: '模型评测',
        icon: 'ExperimentOutlined',
        children: [
          {key: 'benchmarkTests', title: '基准测试'},
          {key: 'customTests', title: '自定义测试'}
        ]
      },
      {
        key: 'humanEvaluation',
        title: '人工评测',
        icon: 'UserOutlined',
        children: [
          {key: 'evalTasks', title: '评测任务'},
          {key: 'evalReports', title: '评测报告'}
        ]
      }
    ]
  },
  {
    key: 'applicationBuilding',
    title: '应用搭建',
    children: [
      {
        key: 'appDashboard',
        title: '应用概览',
        icon: 'DashboardOutlined'
      },
      {
        key: 'modelManagement',
        title: '模型管理',
        icon: 'ApiOutlined',
        children: [
          {key: 'modelLibrary', title: '模型库'},
          {key: 'modelVersions', title: '版本管理'}
        ]
      },
      {
        key: 'promptEngineering',
        title: '提示词工程',
        icon: 'CodeOutlined',
        children: [
          {key: 'promptTemplates', title: '提示词模板'},
          {key: 'promptTesting', title: '提示词测试'}
        ]
      },
      {
        key: 'appCreation',
        title: '应用创建',
        icon: 'BuildOutlined',
        children: [
          {key: 'visualBuilder', title: '可视化搭建'},
          {key: 'appDeployment', title: '应用部署'}
        ]
      }
    ]
  },
  {
    key: 'effectAnalysis',
    title: '效果分析',
    children: [
      {
        key: 'analysisDashboard',
        title: '分析概览',
        icon: 'DashboardOutlined',
        children: [
          {key: 'performanceDashboard', title: '效果大盘'},
          {key: 'trafficDashboard', title: '流量大盘'}
        ]
      },
      {
        key: 'userBehavior',
        title: '会话分析',
        icon: 'InteractionOutlined',
        children: [
          {key: 'userQueries', title: '会话查询'},
          {key: 'SessionDetail', title: '会话详情'},
          {key: 'conversationExplorer', title: '会话查询demo'},
          {key: 'userFeedback', title: '用户反馈'}
        ]
      },
      {
        key: 'baseConfiguration',
        title: '基础配置',
        icon: 'BarChartOutlined',
        children: [
          {key: 'dataTracking', title: '数据埋点'},
          {key: 'sceneConfig', title: '场景配置'},
          {key: 'basicTags', title: '基础标签'},
          {key: 'compositeTags', title: '复合标签'},
          {key: 'tagGroups', title: '标签组'}
        ]
      }
    ]
  },
  {
    key: 'systemSettings',
    title: '系统设置',
    children: [
      {
        key: 'userManagement',
        title: '用户管理',
        icon: 'UserOutlined'
      },
      {
        key: 'rolePermissions',
        title: '角色权限',
        icon: 'SafetyOutlined'
      },
      {
        key: 'apiConfiguration',
        title: 'API配置',
        icon: 'ApiOutlined'
      },
      {
        key: 'systemLogs',
        title: '系统日志',
        icon: 'FileOutlined'
      }
    ]
  }
]

// 仪表盘卡片数据
const dashboardCards = [
  {
    icon: 'DatabaseOutlined',
    title: '数据量',
    value: '128,560',
    description: '标注数据总量'
  },
  {
    icon: 'ExperimentOutlined',
    title: '模型性能',
    value: '94.8%',
    description: '模型准确率'
  },
  {
    icon: 'InteractionOutlined',
    title: '用户互动',
    value: '25,320',
    description: '本周对话次数'
  }
]


// 状态
const selectedKeys = ref(['dataLabeling'])
const sideSelectedKeys = ref(['dashboard'])
const openKeys = ref(['dataManagement', 'labelManagement'])
const collapsed = ref(false)

// 路由初始化 - 修改为inject方式获取router
const router = useRouter ? useRouter() : null

// 根据选中的一级菜单，获取对应的子菜单配置
const currentSubmenu = computed(() => {
  const currentKey = selectedKeys.value[0]
  return menuConfig.find(item => item.key === currentKey)
})

// 处理子菜单点击事件
const handleSubMenuClick = (key) => {
  sideSelectedKeys.value = [key]

  // 使用路由映射表来确定跳转路径
  const routeMap = {
    'userQueries': '/analysis/session',
    'SessionDetail': '/analysis/session-detail/1925799234028777521',
    'conversationExplorer': '/analysis/conversation-explorer',
    'performanceDashboard': '/performance',
    'trafficDashboard': '/traffic',
    'baseConfiguration': '/config/base',
    'dataTracking': '/config/data-tracking',
    'sceneConfig': '/config/scene',
    'basicTags': '/config/basic-tags',
    'compositeTags': '/config/composite-tags',
    'tagGroups': '/config/tag-groups',
    'labelTaskDetail': '/label/task/default'
  }

  // 如果存在路由映射，则进行跳转
  if (routeMap[key] && router) {
    router.push(routeMap[key])
    return
  }

  // 处理带前缀的菜单项
  if (key.startsWith('item-')) {
    const baseKey = key.replace('item-', '')
    if (routeMap[baseKey] && router) {
      router.push(routeMap[baseKey])
      return
    }
  }

  // 找出该子菜单属于哪个一级菜单
  for (const menu of menuConfig) {
    const findInChildren = (items) => {
      for (const item of items) {
        if (item.key === key) return true
        if (item.children) {
          if (findInChildren(item.children)) return true
        }
      }
      return false
    }

    if (menu.children && findInChildren(menu.children)) {
      selectedKeys.value = [menu.key]
      break
    }
  }
}

// 处理主菜单点击事件
const handleMainMenuClick = (menu) => {
  selectedKeys.value = [menu.key]
}
</script>

<style>
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: #f0f2f5;
}

.app {
  min-height: 100vh;
}

.header {
  display: flex;
  align-items: center;
  padding: 0;
  background: white;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}

/* 覆盖 Ant Design 默认样式 */
:deep(.ant-layout-header) {
  background: white !important;
  height: 64px;
  padding: 0;
  line-height: 64px;
}

.logo-container {
  width: 200px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 16px;
  background: white;
  border-right: 1px solid #f0f0f0;
}

.logo {
  color: #1890ff;
  font-size: 24px;
  font-weight: bold;
  white-space: nowrap;
  background: -webkit-linear-gradient(45deg, #1890ff, #36cfc9);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  letter-spacing: 1px;
}

.logo::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: -2px;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, #1890ff, rgba(24, 144, 255, 0));
}

.top-menu {
  line-height: 64px;
  background: white;
  flex: 1;
  border-bottom: none;
}

.top-menu :deep(.ant-menu-item) {
  color: rgba(0, 0, 0, 0.65) !important;
}

.top-menu :deep(.ant-menu-item::after) {
  border-bottom: 2px solid #1890ff !important;
}

.top-menu :deep(.ant-menu-item-selected),
.top-menu :deep(.ant-menu-item:hover) {
  color: #1890ff !important;
  background: rgba(24, 144, 255, 0.1) !important;
}

.sidebar {
  background: white;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.08);
  border-right: 1px solid #f0f0f0;
  position: relative;
  z-index: 9;
}

.sidebar :deep(.ant-layout-sider-trigger) {
  background: #f5f5f5;
  color: rgba(0, 0, 0, 0.65);
  border-top: 1px solid #f0f0f0;
}

.sidebar :deep(.ant-menu-item),
.sidebar :deep(.ant-menu-submenu-title) {
  position: relative;
  transition: all 0.3s ease;
  border-radius: 4px;
  margin: 4px 8px;
}

.sidebar :deep(.ant-menu-item:hover),
.sidebar :deep(.ant-menu-submenu-title:hover) {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  transform: translateY(-1px);
}

.sidebar :deep(.ant-menu-item-selected) {
  background: #e6f7ff !important;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.content {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 64px);
}

.content-container {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.05);
  min-height: calc(100vh - 64px - 48px);
}

.dashboard-cards {
  margin-top: 24px;
}

.dashboard-card {
  border-radius: 8px;
  transition: all 0.3s;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transform: translateY(0);
}

.dashboard-card:hover {
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-5px);
}

.dashboard-card h2 {
  font-size: 28px;
  margin: 12px 0 8px;
  color: #1890ff;
}

.dashboard-card p {
  color: rgba(0, 0, 0, 0.45);
  margin-bottom: 0;
}
</style>
