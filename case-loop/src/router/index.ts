import {createRouter, createWebHistory} from 'vue-router';
import Dashboard from '../views/Dashboard.vue';
import TaskPanel from '../views/TaskPanel.vue';
import CallMonitor from '../views/CallMonitor.vue';
import CaseList from '../views/CaseList.vue';
import CaseDetail from '../views/CaseDetail.vue';
import TrafficDashboard from '../views/traffic/TrafficDashboard.vue';
import PerformanceDashboard from '../components/PerformanceDashboard.vue';
import SessionAnalysis from '../views/analysis/SessionAnalysis.vue';
import SessionDetail from '../views/analysis/SessionDetail.vue';
import ConversationExplorer from '../components/ConversationExplorer.vue';
import BaseConfiguration from '../components/BaseConfiguration.vue';
import SceneConfiguration from '../components/SceneConfiguration.vue';
import BasicTags from '../components/BasicTags.vue';
import CompositeTags from '../components/CompositeTags.vue';
import TagGroups from '../components/TagGroups.vue';
import DataTracking from '../components/DataTracking.vue';
import LabelTaskDetail from '../views/label/LabelTaskDetail.vue';
import EffectOverviewView from '../views/effect/Overview.vue';
import ProblemAnalysis from '../views/effect/ProblemAnalysis.vue';
// import LabelLayout from '../views/label/LabelLayout.vue';
// import ConfigLayout from '../views/config/ConfigLayout.vue';
import Index from '../views/effect/Index.vue';

const routes = [
    {
        path: '/',
        name: 'Dashboard',
        component: Dashboard
    },
    {
        path: '/task-panel',
        name: 'TaskPanel',
        component: TaskPanel
    },
    {
        path: '/call-monitor',
        name: 'CallMonitor',
        component: CallMonitor
    },
    {
        path: '/traffic',
        name: 'TrafficDashboard',
        component: TrafficDashboard
    },
    {
        path: '/performance',
        name: 'PerformanceDashboard',
        component: PerformanceDashboard
    },
    {
        path: '/case-list',
        name: 'CaseList',
        component: CaseList
    },
    {
        path: '/case/:id',
        name: 'CaseDetail',
        component: CaseDetail,
        props: true
    },
    {
        path: '/analysis/session',
        name: 'SessionAnalysis',
        component: SessionAnalysis
    },
    {
        path: '/analysis/session-detail/:sessionId',
        name: 'SessionDetail',
        component: SessionDetail,
        props: true
    },
    {
        path: '/analysis/conversation-explorer',
        name: 'ConversationExplorer',
        component: ConversationExplorer
    },
    {
        path: '/effect',
        name: 'Effect',
        component: Index,
        children: [
            {
                path: 'overview',
                name: 'EffectOverview',
                component: EffectOverviewView,
            },
            {
                path: 'problem-analysis',
                name: 'ProblemAnalysis',
                component: ProblemAnalysis,
            },
        ],
    },
    // 新增配置路由
    // {
    //     path: '/config',
    //     name: 'Configuration',
    //     component: ConfigLayout,
    //     children: [
    //         {
    //             path: 'base',
    //             name: 'BaseConfiguration',
    //             component: BaseConfiguration
    //         },
    //         {
    //             path: 'scene',
    //             name: 'SceneConfiguration',
    //             component: SceneConfiguration
    //         },
    //         {
    //             path: 'basic-tags',
    //             name: 'BasicTags',
    //             component: BasicTags
    //         },
    //         {
    //             path: 'composite-tags',
    //             name: 'CompositeTags',
    //             component: CompositeTags
    //         },
    //         {
    //             path: 'tag-groups',
    //             name: 'TagGroups',
    //             component: TagGroups
    //         },
    //         {
    //             path: 'data-tracking',
    //             name: 'DataTracking',
    //             component: DataTracking
    //         }
    //     ]
    // },
    // 标注任务相关路由
    // {
    //     path: '/label',
    //     name: 'Label',
    //     component: LabelLayout,
    //     children: [
    //         {
    //             path: 'task/:taskId',
    //             name: 'LabelTaskDetail',
    //             component: LabelTaskDetail,
    //             props: true
    //         }
    //     ]
    // }
];

const router = createRouter({
    history: createWebHistory(import.meta.env.BASE_URL),
    routes
})

export default router
