<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.meituan.csc.aigc.eval</groupId>
        <artifactId>csc-eval</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>csc-eval-service</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.sankuai.csccratos</groupId>
            <artifactId>csc-aida-label-client</artifactId>
            <version>0.0.2-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.csccratos</groupId>
            <artifactId>aida-java-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>csc-ivr-adapter-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.googlecode.aviator</groupId>
            <artifactId>aviator</artifactId>
            <version>5.2.6</version> <!-- 使用最新版本 -->
        </dependency>
        <dependency>
            <groupId>com.meituan.csc.aigc.eval</groupId>
            <artifactId>csc-eval-service-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.csc.aigc</groupId>
            <artifactId>prompt-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.csc.aigc.aida</groupId>
            <artifactId>csc-aigc-aida-labeling-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>mss-java-sdk-s3</artifactId>
        </dependency>


        <dependency>
            <groupId>com.sankuai.it.sso</groupId>
            <artifactId>sso-java-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-pigeon</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>dpsf-net</artifactId>
                    <groupId>com.dianping.dpsf</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-zebra</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-mafka</artifactId>
        </dependency>

        <!-- Mybatis -->
        <dependency>
            <artifactId>mybatis</artifactId>
            <groupId>org.mybatis</groupId>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-spring</artifactId>
        </dependency>
        <!-- Mybatis-Plus -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.csc.aigc</groupId>
            <artifactId>runtime-service-api</artifactId>
            <version>0.1.39</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.csccratos</groupId>
            <artifactId>csc-aida-workflow-client</artifactId>
            <version>${aida-workflow-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.csccratos</groupId>
            <artifactId>aida-config-client</artifactId>
            <version>${aida-config-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.csccratos</groupId>
            <artifactId>aida-common-dao</artifactId>
            <version>${aida-common-dao.version}</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.xm</groupId>
            <artifactId>xm-pub-api-client</artifactId>
        </dependency>
        <!--文枢-->
        <dependency>
            <groupId>com.meituan.sec</groupId>
            <artifactId>distribute-thrift-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>sep-bluewhale-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cip.crane</groupId>
            <artifactId>crane-client</artifactId>
            <version>1.4.2</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.talostwo</groupId>
            <artifactId>talostwo-sdk-java</artifactId>
            <version>1.8.1-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.cip</groupId>
            <artifactId>pike-message-sdk</artifactId>
            <version>2.1.9-RELEASE</version>
        </dependency>
        <!--AC-->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>csc-aircraft-api</artifactId>
            <version>0.3.5</version>
        </dependency>
        <!--FAQ-->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>csc-worksheet-api</artifactId>
            <version>0.1.34</version>
            <exclusions>
                <exclusion>
                    <groupId>org.mockito</groupId>
                    <artifactId>mockito-all</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 门户消息 -->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>csc-portal-message-api</artifactId>
            <version>0.0.2</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>csc-portal-open-api</artifactId>
            <version>0.0.24</version>
        </dependency>
        <!-- ES -->
        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
            <version>7.10.2-mt4</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>poros-high-level-client</artifactId>
            <version>0.9.22_ES7</version>
        </dependency>
        <!-- flow -->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>csc-flow-common-api</artifactId>
            <version>0.3.24</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>sep-nps-biz-api</artifactId>
            <version>1.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>csc-faq-config-api</artifactId>
            <version>1.0.54</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>csc-flow-config-api</artifactId>
            <version>0.2.89</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>csc-eagle-query-api</artifactId>
            <version>0.1.3</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>csc-dict-config-api</artifactId>
            <version>0.0.14</version>
        </dependency>
        <!-- 木星系统双声道通话录音  -->
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>call_sdk</artifactId>
            <version>2.9.27</version>
        </dependency>
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
            <version>2.9.3</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>