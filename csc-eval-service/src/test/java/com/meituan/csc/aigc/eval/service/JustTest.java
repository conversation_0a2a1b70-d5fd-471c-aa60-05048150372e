package com.meituan.csc.aigc.eval.service;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.csc.aircraft.api.dto.ACInterfaceDTO;
import com.dianping.csc.aircraft.api.dto.ModelPropertyDTO;
import com.dianping.csc.aircraft.api.dto.inner.ModelDTO;
import com.dianping.csc.aircraft.api.enums.ModelTypeEnum;
import com.dianping.csc.aircraft.api.service.inner.IFaceRemoteService;
import com.dianping.csc.aircraft.api.service.inner.ModelRemoteService;
import com.dianping.csc.center.engine.worksheet.faq.FaqService;
import com.dianping.csc.center.engine.worksheet.faq.dto.FaqBusinessTypeDTO;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.meituan.csc.aigc.eval.Application;
import com.meituan.csc.aigc.eval.config.http.HttpConfig;
import com.meituan.csc.aigc.eval.param.gpt.ChatGptHttpRequest;
import com.meituan.csc.aigc.eval.proxy.RedisClientProxy;
import com.meituan.csc.aigc.eval.service.analysis.es.OnlineSessionUpsertService;
import com.meituan.csc.aigc.eval.utils.HttpUtil;
import lombok.Data;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.Serializable;
import java.util.*;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
public class JustTest {

    @Autowired
    private RedisClientProxy redisStoreClient;

    @Autowired
    private ModelRemoteService modelRemoteService;

    @Autowired
    OnlineSessionUpsertService onlineSessionUpsertService;

    @Test
    public void estsd(){
        List<ModelPropertyDTO> modelPropertyList = modelRemoteService.listProperties(17L);
        System.out.println(modelPropertyList);
    }

    @Test
    public void test() {
        redisStoreClient.set(new StoreKey("test_category", "test_key"), "123");
    }

//    @Test
//    public void test11() {
//        System.out.println(onlineSessionUpsertService.getSessionInfoFromPortal("1920733574466711637"));
//        System.out.println(onlineSessionUpsertService.getRobotInfoFromPortalRobot("1920733574466711637"));
//    }

    @Autowired
    private HttpConfig httpConfig;

    @Test
    @Ignore
    public void testGlm() {
        String apiKey = "eyJhbGciOiJIUzI1NiIsInNpZ25fdHlwZSI6IlNJR04iLCJ0eXAiOiJKV1QifQ.eyJhcGlfa2V5IjoiZjE0MmVmOTJhNTRjY2NmNDZlNDM2N2QxYWMwODJkZWEiLCJleHAiOjE4MDM1NDkxMTkyMjEsInRpbWVzdGFtcCI6MTcwODk0MTExOTIyMX0.zdte69bELR6zNM0wOU6vzuwavJ8c9Xa5_Q0nVirteno";
        String url = "https://open.bigmodel.cn/api/paas/v4/chat/completions";
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        headers.add("Authorization", apiKey);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("model", "glm-4");
        List<ChatGptHttpRequest.GptMessage> messageList = new ArrayList<>();
        ChatGptHttpRequest.GptMessage message = new ChatGptHttpRequest.GptMessage();
        message.setRole("user");
        message.setContent("1+3=？");
        messageList.add(message);
        jsonObject.put("messages", messageList);
        String result = HttpUtil.sendPost(JSON.toJSONString(jsonObject), headers, url, String.class, httpConfig.getRestTemplate());
        System.out.println(result);
    }

    public static void main(String[] args) {
        String a = "[{\"slotCode\":\"sessionId\",\"slotName\":\"会话id\",\"slotTimestamp\":1721710903594,\"slotValue\":\"1815613052394172508\"},{\"slotCode\":\"buId\",\"slotName\":\"业务ID\",\"slotTimestamp\":1721710903594,\"slotValue\":\"179\"},{\"slotCode\":\"visitId\",\"slotName\":\"门户访问id\",\"slotTimestamp\":1721710903594,\"slotValue\":\"access-5f2cf174-6510-4625-a75f-74154ee2d97c\"},{\"slotCode\":\"subBuId\",\"slotName\":\"子业务ID\",\"slotTimestamp\":1721710903594,\"slotValue\":\"1333\"},{\"slotCode\":\"taskProcessInstanceId\",\"slotName\":\"task实例ID\",\"slotTimestamp\":1721710903594,\"slotValue\":\"c329c356-cd1f-49b2-bee4-201322e6f390\"},{\"slotCode\":\"userQuery\",\"slotName\":\"用户输入\",\"slotTimestamp\":1721710903594,\"slotValue\":\"1个多多小时都没有送到\"},{\"slotCode\":\"userId\",\"slotName\":\"用户id\",\"slotTimestamp\":1721710903594,\"slotValue\":\"3825448224\"},{\"slotCode\":\"userType\",\"slotName\":\"用户类型\",\"slotTimestamp\":1721710903594,\"slotValue\":\"40\"},{\"slotCode\":\"orderId\",\"slotName\":\"订单id\",\"slotTimestamp\":1721710903594,\"slotValue\":\"3801172841489410427\"}]";
        List<SlotData> slotDataList = JSONObject.parseArray(a, SlotData.class);
        String b = "[{\"slotCode\":\"isPreOrderDesc\",\"slotName\":\"是否为预订单\",\"slotTimestamp\":1721710904309,\"slotValue\":\"否\"},{\"slotCode\":\"isCourierAcceptOrder\",\"slotName\":\"骑手是否已接单\",\"slotTimestamp\":1721710904309,\"slotValue\":\"是 骑手接单时间=20240723 11:36:11\"},{\"slotCode\":\"isCourierGetOrder\",\"slotName\":\"骑手是否已取餐\",\"slotTimestamp\":1721710904309,\"slotValue\":\"是 骑手取餐时间=20240723 11:50:12\"},{\"slotCode\":\"overTimeInfo\",\"slotName\":\"订单是否超时\",\"slotTimestamp\":1721710904309,\"slotValue\":\"是 订单超时时间=53分钟\"},{\"slotCode\":\"hasDelivered\",\"slotName\":\"订单是否已送达\",\"slotTimestamp\":1721710904309,\"slotValue\":\"否\"},{\"slotCode\":\"isMake\",\"slotName\":\"商家是否已出餐\",\"slotTimestamp\":1721710904309,\"slotValue\":\"是 商家出餐时间=1721705782\"},{\"slotCode\":\"actualPayTotal\",\"slotName\":\"订单金额\",\"slotTimestamp\":1721710904309,\"slotValue\":\"12.1元\"},{\"slotCode\":\"isTransfer\",\"slotName\":\"订单是否被转交派送\",\"slotTimestamp\":1721710904309,\"slotValue\":\"否\"},{\"slotCode\":\"orderTime\",\"slotName\":\"用户是否已下单\",\"slotTimestamp\":1721710904309,\"slotValue\":\"是 下单时间为20240723 11:25:04\"},{\"slotCode\":\"earliestDeliveryTime\",\"slotName\":\"骑手最早送达时间\",\"slotTimestamp\":1721710904309,\"slotValue\":\"否\"},{\"slotCode\":\"isHighValueUser\",\"slotName\":\"是否高价值用户\",\"slotTimestamp\":1721710904309,\"slotValue\":\"否\"},{\"slotCode\":\"latestDeliveryTime\",\"slotName\":\"骑手最晚送达时间\",\"slotTimestamp\":1721710904309,\"slotValue\":\"20240723 12:15:47\"},{\"slotCode\":\"isRiderReportException\",\"slotName\":\"骑手是否上报异常\",\"slotTimestamp\":1721710904309,\"slotValue\":\"联系不上顾客-验证成功\"},{\"slotCode\":\"isBuyOnTimeDelivery\",\"slotName\":\"用户是否购买准时宝\",\"slotTimestamp\":1721710904309,\"slotValue\":\"是\"},{\"slotCode\":\"refundDetail\",\"slotName\":\"订单退款详情\",\"slotTimestamp\":1721710904309,\"slotValue\":\"退款申请类型：用户被商家拒绝后申诉。退款原因：超时还未收到餐，超时了。退款申请结果：等待处理中。\"},{\"slotCode\":\"isBuyAssuredFood\",\"slotName\":\"用户是否购买放心吃\",\"slotTimestamp\":1721710904309,\"slotValue\":\"是\"},{\"slotCode\":\"chatRecode\",\"slotName\":\"用户和商家或者骑手的聊天记录\",\"slotTimestamp\":1721710904309,\"slotValue\":\"【第1段聊天记录】开始\\n骑手:16楼上不去，要刷卡\\n骑手:酒店只能到10楼\\n骑手:而且打你几十个电话都打不通\\n骑手:打了都是无法接通\\n骑手:而且楼下也不让上去\\n骑手:我现在在送其他单了\\n\\n【第1段聊天记录】结束\"},{\"slotCode\":\"estimateDeliveryTime\",\"slotName\":\"预计送达时间\",\"slotTimestamp\":1721710904309,\"slotValue\":\"20240723 12:07:47\"},{\"slotCode\":\"callRecord\",\"slotName\":\"外呼记录\",\"slotTimestamp\":1721710904309,\"slotValue\":\"空\"},{\"slotCode\":\"isMerchantOrderAc\",\"slotName\":\"商家是否接单\",\"slotTimestamp\":1721710904309,\"slotValue\":\"是 商家接单时间=20240723 11:34:48\"},{\"slotCode\":\"isMerchantDelivery\",\"slotName\":\"订单是否商家自配送\",\"slotTimestamp\":1721710904309,\"slotValue\":\"否\"},{\"slotCode\":\"isPinfanOrder\",\"slotName\":\"是否拼好饭订单\",\"slotTimestamp\":1721710904309,\"slotValue\":\"是\"}]";
        List<SlotData> slotDataList2 = JSONObject.parseArray(b, SlotData.class);


        List<List<String>> dataList = new ArrayList<>();
        List<List<String>> headList = new ArrayList<>();

        List<String> data = new ArrayList<>();
        dataList.add(data);

        for (SlotData slotData : slotDataList) {
            List<String> head = new ArrayList<>();
            head.add(slotData.getSlotName());
            headList.add(head);

            List<String> data1 = dataList.get(0);
            data.add(slotData.getSlotValue());
        }

        for (SlotData slotData : slotDataList2) {
            List<String> head = new ArrayList<>();
            head.add(slotData.getSlotName());
            headList.add(head);
            List<String> data1 = dataList.get(0);
            data.add(slotData.getSlotValue());
        }


        EasyExcelFactory.write("/Users/<USER>/Desktop/变量啊2.xlsx").sheet(0).head(headList).doWrite(dataList);
    }

    @Data
    public static class SlotData implements Serializable {
        private String slotName;
        private String slotValue;
    }
}
