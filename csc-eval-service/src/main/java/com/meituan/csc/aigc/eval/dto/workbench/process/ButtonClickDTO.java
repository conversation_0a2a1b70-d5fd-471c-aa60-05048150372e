package com.meituan.csc.aigc.eval.dto.workbench.process;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 按钮点击数据传输对象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ButtonClickDTO implements Serializable {
    @JsonProperty("buttonClickId")
    private Integer buttonClickId;

    @JsonProperty("buttonClickName")
    private String buttonClickName;
}
