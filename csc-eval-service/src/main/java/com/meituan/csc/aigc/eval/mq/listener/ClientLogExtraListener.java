package com.meituan.csc.aigc.eval.mq.listener;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.common.collect.Maps;
import com.meituan.csc.aigc.eval.constants.CatConstants;
import com.meituan.csc.aigc.eval.constants.EsScriptConstants;
import com.meituan.csc.aigc.eval.dto.es.AidaOnlineSessionCacheDTO;
import com.meituan.csc.aigc.eval.dto.mq.ExtraLog;
import com.meituan.csc.aigc.eval.service.analysis.OnlineSessionRedisService;
import com.meituan.csc.aigc.eval.service.analysis.es.CaseAnalysisEagleService;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mdp.boot.starter.mafka.consumer.anno.MdpMafkaMsgReceive;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/14
 */
@Slf4j
@Service("clientLogExtraListener")
public class ClientLogExtraListener {

    @Autowired
    private OnlineSessionRedisService redisService;

    @Autowired
    private CaseAnalysisEagleService eagleService;

    /**
     * 日志前缀
     */
    private static final String LOG_PREFIX = "[ClientLogExtraListener]";


    @MdpMafkaMsgReceive
    public ConsumeStatus receive(String msgBody) {
        Transaction transaction = Cat.newTransaction(CatConstants.ONLINE_SESSION_MQ_TYPE, CatConstants.ONLINE_SESSION_CLIENT_LOG);
        try {
            // 解析消息体为 ExtraLog 对象
            ExtraLog extraLog = JSON.parseObject(msgBody, ExtraLog.class);
            if (extraLog == null || extraLog.getSessionId() == null) {
                log.error("{}-ExtraLog参数解析异常, extraLog = {}", LOG_PREFIX, msgBody);
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            String sessionId = String.valueOf(extraLog.getSessionId());
            // 是否进入AI搭
            AidaOnlineSessionCacheDTO sessionCacheDTO = redisService.getSessionInfo(sessionId);
            if (sessionCacheDTO == null) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            String visitId = extraLog.getVisitId();

            Map<String, Object> map = Maps.newHashMap();
            String script = "";
            if (StringUtils.isNotBlank(visitId) && !sessionCacheDTO.getVisitIdList().contains(visitId)) {
                script += EsScriptConstants.VISIT_ID_LIST_ADD;
                map.put(EsScriptConstants.VISIT_ID_PARAM, visitId);
            }
            if (StringUtils.isBlank(script)) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            // 尝试获取锁
            if (!redisService.getSessionLock(sessionId)) {
                log.error("{}-获取锁失败, sessionId = {}, sessionCacheDTO = {}", LOG_PREFIX, sessionId, JSON.toJSONString(sessionCacheDTO));
                return ConsumeStatus.CONSUME_FAILURE;
            }
            try {
                // 涉及sessionCacheDTO的更新 加锁后重查一次 保证幂等
                sessionCacheDTO = redisService.getSessionInfo(sessionId);
                sessionCacheDTO.getVisitIdList().add(visitId);
                // 保存到ES
                eagleService.upsertByScript(sessionCacheDTO.getSessionIndex(), sessionId, script, map);
                // 更新visitId缓存
                redisService.setSessionInfo(sessionId, sessionCacheDTO);
            } catch (Exception e) {
                log.error("{}-保存visitId异常, sessionId = {}, e:", LOG_PREFIX, sessionId, e);
                transaction.setStatus(e);
            } finally {
                redisService.deleteSessionLock(sessionId);
            }
        } catch (Exception e) {
            log.error("{}-消费失败, msgBody = {}", LOG_PREFIX, msgBody, e);
            transaction.setStatus(e);
        } finally {
            transaction.complete();
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
