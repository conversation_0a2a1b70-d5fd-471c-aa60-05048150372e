package com.meituan.csc.aigc.eval.constants;

/**
 * <AUTHOR>
 */
public class LionConstants {

    public static final String GPT_APP_ID_MAP = "gpt.app.id.map";

    public static final String GPT_DEFAULT_PARAM_MAP = "gpt.default.param.map";

    public static final String CHAT_GPT_HTTP_URL_NEW = "chat.gpt.http.url.new";

    public static final String EVAL_ARENA_CONFIG_MODEL_LIST = "eval.arena.config.model.list";
    public static final String EVAL_ARENA_CONFIG_DATASET_LIST = "eval.arena.config.dataset.list";

    public static final String CHAT_GPT_APP_ID = "chat.gpt.appId";
    public static final String MT_TEXT_HTTP_URL = "mt.text.http.url";

    public static final String EVAL_ADMIN_KEY = "eval.admin.key";

    public static final String EVAL_RUNNING_CACHE_EXPIRE_TIME = "eval.running.cache.expire.time";

    public static final String EVAL_TASK_TRAINDATASET_MAP = "eval.task.trainDataset.map";
    public static final String TRAIN_DATASET_VERSION_EXPIRE_TIME = "eval.train.dataset.version.cache.expire.time";
    public static final String TRAIN_DATASET_SAVE_BATCH_SIZE = "eval.train.dataset.batch.size";
    public static final String ARENA_MODEL_OUTPUT_EXPIRE_TIME = "arena.model.output.expire.time";

    public static final String MODEL_CONFIG_LIST = "model.config.list";

    public static final String SCENE_LIST = "scene.list";

    public static final String EXECUTE_PARALLEL_MAP = "execute.parallel.map";
    public static final String EVAL_TASK_AUTO_INSPECT_MAP = "eval.task.autoInspect.map";

    public static final String INSPECT_FAILED_REASON = "inspect.failed.reason";

    public static final String AIDA_INNER_SECRET = "aida.inner.secret";

    public static final String AIDA_INTERCEPT_LIST = "aida.intercept.list";

    public static final String COMPUTE_METHOD = "compute.method";

    public static final String AGENT_MARK_TASK_LIST_URL = "agent.mark.task.list.url";

    public static final String DATA_PULL_CONFIG_SOURCE = "data.pull.config.source";

    public static final String PULL_DATA_USER_LIST = "pull.data.user.list";

    public static final String DATA_PULL_HIVE_SQL_CONFIG = "data.pull.hive.sql.config";

    public static final String UPDATE_BATCH_SIZE = "update.batch.size";

    public static final String DATA_PULL_HIVE_MAX_SIZE = "data.pull.hive.max.size";

    public static final String DATA_PULL_AC_CONFIG = "data.pull.ac.config";

    public static final String DATA_PULL_INTERFACE_ID = "data.pull.interface.id";
    public static final String DATASET_URL = "dataset.url";

    public static final String ONLINE_MOCK_DATA = "online.mock.data";

    public static final String CONTENT_CHECK_SIZE = "content.check.size";

    public static final String ROBOT_ID_TEMPLATE = "robot.id.template";

    public static final String AC_INVOKE_SLEEP_TIME = "ac.invoke.sleep.time";

    public static final String ALL_ROBOT_CONFIG = "all.robot.config";

    public static final String ROBOT_MOCK_MAX_TURN = "robot.mock.max.turn";

    /**
     * 话术模型参数配置
     */
    public static final String DIALOG_MODEL_PARAM = "dialog.model.param";

    /**
     * 机器模拟机器首轮
     */
    public static final String MODEL_INITIAL_CONTEXT = "model.initial.context";

    /**
     * pike的bizId
     */
    public static final String PIKE_BIZ_ID = "pike.biz.id";

    /**
     * AI搭空间维度评测页面url
     */
    public static final String AIDA_EVAL_WORKSPACE_URL = "aida.eval.workspace.url";

    /**
     * AI搭应用维度评测页面url
     */
    public static final String AIDA_EVAL_APP_URL = "aida.eval.app.url";
    /**
     * 评测任务并行数量配置
     */
    public static final String EVAL_TASK_PARALLEL_NUM = "eval.task.parallel.num";
    /**
     * 用户模拟器列表
     */
    public static final String EVAL_ROBOT_MOCK_LIST = "eval.robot.mock.list";

    /**
     * 外呼槽位映射关系
     */
    public static final String CALL_OUT_SLOT_MAP = "call.out.slot.map";

    /**
     * 用户模拟器熔断关键词匹配
     */
    public static final String ROBOT_MOCK_FUSE_CONTENT = "robot.mock.fuse.content";
    /**
     * 模型输出多句话的分隔符
     */
    public static final String MODEL_OUTPUT_SPLIT = "model.output.split";
    /**
     * 在线侧消息获取数量
     */
    public static final String ONLINE_MESSAGE_SIZE = "online.message.size";

    /**
     * 用户消息类型列表
     */
    public static final String USER_MESSAGE_TYPE_LIST = "user.message.type.list";

    /**
     * 自定义的消息类型处理器
     */
    public static final String MESSAGE_TYPE_ENUM_CUSTOMIZE_HANDLE = "chat.message.customize.handle";

    /**
     * 过滤的消息类型
     */
    public static final String FILTER_MESSAGE_TYPE = "filter.message.type";

    public static final String WORKBENCH_FUYAO_SERVICE_PROCESS_URL = "workbench.fuyao.service.process.url";
    public static final String WORKBENCH_FUYAO_SERVICE_PROCESS_CONTENT = "workbench.fuyao.service.process.content";
    public static final String WORKBENCH_QUERY_MESSAGE_COUNT = "workbench.query.message.count";
    /**
     * IVR过滤的消息类型
     */
    public static final String WORKBENCH_IVR_FILTER_MESSAGE_TYPE = "workbench.ivr.filter.message.type";

    /**
     * IVR过滤的广播目的
     */
    public static final String WORKBENCH_IVR_FILTER_BROADCAST_PURPOSE = "workbench.ivr.filter.broadcast.purpose";


    /**
     * 工作台机器指标与人工指标映射关系
     */
    public static final String WORKBENCH_SESSION_METRIC_RELATION = "workbench.session.metric.relation";


    /**
     * PB场景与工作空间映射关系
     */
    public static final String WORKBENCH_SCENE_WORKSPACE_RELATION = "workbench.scene.workspace.relation";

//    public static final String WORKBENCH_SCENE_MOCK_CONFIG = "workbench.scene.mock.config";


    public static final String WORKBENCH_QUERY_COLLECT_LOCK_EXPIRE_TIME = "workbench.query.collect.lock.expire.time";
    public static final String WORKBENCH_QUERY_COLLECT_LOCK_WAIT_TIME = "workbench.query.collect.lock.wait.time";
    public static final String WORKBENCH_AUTO_INSPECTION_LOCK_EXPIRE_TIME = "metric.inspection.lock.expire.time";
    public static final String WORKBENCH_AUTO_INSPECTION_CACHE_MAX_RETRIES = "auto.inspection.cache.retry.count";
    public static final String WORKBENCH_AUTO_INSPECTION_CACHE_RETRY_INTERVAL_TIME = "auto.inspection.cache.retry.interval";
    public static final String WORKBENCH_METRIC_RESULT_CACHE_EXPIRE_TIME = "workbench.metric.result.cache.expire.time";

    public static final String WORKBENCH_AIDA_WORKSPACE_APPLICATION = "workbench.aida.workspace.application";

    /**
     * 是否使用数据库查询会话信息
     */
    public static final String WORKBENCH_SESSION_DB_SWITCH = "workbench.session.db.switch";

    /**
     * AI搭执行动作节点信息
     */
    public static final String AIDA_ACTION_NODE_INFO = "aida.action.node.info";

    /**
     * 子流程的配置
     */
    public static final String SUB_APPLICATION_CONFIG = "sub.application.config";

    /**
     * 特殊解析的信号
     */
    public static final String SPECIAL_PARSE_SIGNAL = "special.parse.signal";

    /**
     * LLM校验开关
     */
    public static final String LLM_CHECK_SWITCH = "llm.check.switch";

    /**
     * 评测任务限制配置
     */
    public static final String EVAL_TASK_LIMIT_CONFIG = "eval.task.limit.config";

    /**
     * 配置agent的首个大模型节点映射
     */
    public static final String AGENT_FIRST_BIG_MODEL_NODE = "agent.first.big.model.node";

    /**
     * 人工标注应用通用的应用，配置在lion
     */
    public static final String MANUAL_MARK_APPLICATION = "manual.mark.application";
    /**
     * 解析聊天角色
     */
    public static final String CHAT_MESSAGE_ROLE = "chat.message.role";


    /**
     * 获取在线的会话筛选项配置
     */
    public static final String WORKBENCH_ONLINE_FILTER_CONDITION_CONFIG = "workbench.online.filter.condition.config";

    /**
     * 分页查询AI搭数据控制
     */
    public static final String AIDA_PAGE_SESSION_CONFIG = "aida.page.session.config";
    public static final String DATASET_VERSION_DETAIL_SIZE_LIMIT = "dataset_version_detail_size_limit";

    /**
     * LLM过滤消息
     */
    public static final String LLM_FILTER_MESSAGE = "llm.filter.message";

    /**
     * 大表治理-复杂改造是否调用新接口开关
     */
    public static final String ENABLE_NEW_API_WITH_APP_ID_SWITCH_LIST = "enable.new.api.with.app.id.switch.list";

    /**
     * 需要识别为llm消息的sender类型
     */
    public static final String WORKBENCH_LLM_SENDER_TYPE_LIST = "workbench.llm.sender.type.list";
    /**
     * 工作台用户角色
     */
    public static final String WORKBENCH_USER_ROLE_MAP = "workbench.user.role.map";

    /**
     * 工作台导出会话数量限制
     */
    public static final String WORKBENCH_EXPORT_SESSION_LIMIT = "workbench.export.session.limit";

    /**
     * 创建评测任务页面展示多空间的权限mis配置
     */
    public static final String EVAL_TASK_SHOW_AUTH_SPACES = "eval_task_show_auth_spaces";

    /**
     * 评测系统跳转链接统一管理
     */
    public static final String EVAL_TASK_JUMP_URL = "eval_task_jump_url";

    /**
     * AI搭work flow 下探深度
     */
    public static final String AIDA_WORK_FLOW_NODE_DEPT_MAX = "aida.work.flow.node.dept.max";

    /**
     * AI搭work flow 调试节点白名单
     */
    public static final String AIDA_WORK_FLOW_DEBUG_WHITE_LIST_NODE = "aida.work.flow.debug.white.list.node";

    /**
     * AI搭work flow 调试节点黑名单
     */
    public static final String AIDA_WORK_FLOW_DEBUG_BLACK_LIST_NODE = "aida.work.flow.debug.black.list.node";

    /**
     * 获取Aida机器人树形状节点信息缓存过期时间
     */
    public static final String AIDA_WORK_FLOW_LLM_TREE_CACHE_EXPIRE_TIME = "aida.work.flow.llm.tree.cache.expire.time";

    public static final String EVAL_TASK_BUSINESS_PARAM_PERMISSION = "eval_task_business_param_permission";

    /**
     * 分析台缓存过期时间
     */
    public static final String WORKBENCH_CACHE_EXPIRE_TIME = "workbench.cache.expire.time";

    /**
     * 缓存字符最大长度
     */
    public static final String CACHE_MAX_LENGTH = "cache.max.length";

    /**
     * 会话分析在线会话ES写入开关
     */
    public static final String ANALYSIS_ONLINE_UPSERT_SWITCH = "analysis.online.upsert.switch";

    /**
     * 自动化分析任务并行总数量
     */
    public static final String AUTO_ANALYZE_TASK_PARALLEL_NUM = "auto.analyze.task.parallel.num";

    /**
     * 自动化分析单任务的session并行数量
     */
    public static final String AUTO_ANALYZE_SESSION_PARALLEL_NUM = "auto.analyze.session.parallel.num";

    /**
     * 自动化分析工具-调用aida机器人进行自动化分析的鉴权
     */
    public static final String AUTO_ANALYZE_TOOL_AIDA_ROBOT_AUTH = "auto.analyze.tool.aida.robot.auth";

    /**
     * 自动化分析工具-调用aida机器人的超时时间
     */
    public static final String AUTO_ANALYZE_TOOL_AIDA_ROBOT_TIMEOUT = "auto.analyze.tool.aida.robot.timeout";

    /**
     * 自动化分析工具域名
     */
    public static final String AUTO_ANALYZE_TOOL_DOMAIN = "auto.analyze.tool.domain";

    /**
     * 自动化分析工具-文件行数限制
     */
    public static final String AUTO_ANALYZE_TOOL_FILE_ROWS_LIMIT = "auto.analyze.tool.file.rows.limit";

    /**
     * 自动化分析工具-文件大小限制
     */
    public static final String AUTO_ANALYZE_TOOL_FILE_SIZE_LIMIT = "auto.analyze.tool.file.size.limit";

    /**
     * 自动化分析工具-是否终止分析任务
     */
    public static final String AUTO_ANALYZE_TOOL_TERMINATE_ANALYSIS_TASK = "auto.analyze.tool.terminate.analysis.task";

    /**
     * 自动化分析工具-使用http接口调用aida机器人开关
     */
    public static final String AUTO_ANALYZE_TOOL_USE_HTTP_INTERFACE_SWITCH = "auto.analyze.tool.use.http.interface.switch";

    /**
     * 自动化分析工具-调用aida机器人重试次数
     */
    public static final String AUTO_ANALYZE_TOOL_AIDA_ROBOT_RETRY_COUNT = "auto.analyze.tool.aida.robot.retry.count";

    /**
     * 自动化分析工具-训练系统URL前缀
     */
    public static final String AUTO_ANALYZE_TOOL_TASK_MANAGEMENT_URL_PREFIX = "auto.analyze.tool.task.management.url.prefix";

    /**
     * case分析会话数据延迟同步时间
     */
    public static final String CASE_ANALYSIS_SESSION_DATA_SYNC_DELAY_TIME = "case.analysis.session.data.sync.delay.time";

    /**
     * case分析对话延迟同步时间
     */
    public static final String CASE_ANALYSIS_SESSION_DIALOG_SYNC_DELAY_TIME = "case.analysis.session.dialog.sync.delay.time";
}
