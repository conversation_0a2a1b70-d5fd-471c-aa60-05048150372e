package com.meituan.csc.aigc.eval.dto.workbench.process.enums;

/**
 * 会话分析-[用户问题及解决方案]服务过程-答案类型
 */
public enum RobotSolutionType {

    TypicalSolution("typicalSolution", "通用答案"),
    FaqSolution("faqSolution", "Faq答案"),
    TaskSolution("taskSolution", "Task答案"),
    RecommendSolution("recommendSolution", "推荐解决方案"),
    OtherSolution("otherSolution", "其他答案"),
    AiModelSolution("aiModelSolution", "大模型答案"),
    TaskAiDaSolution("taskAiDaSolution", "Task答案(大模型)"),
    ;

    private String code;
    private String name;

    RobotSolutionType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }
}

