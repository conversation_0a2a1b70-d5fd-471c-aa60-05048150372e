package com.meituan.csc.aigc.eval.proxy;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.csc.eagle.query.dto.Response;
import com.dianping.csc.eagle.query.dto.page.PageDTO;
import com.dianping.csc.eagle.query.dto.portal.*;
import com.dianping.csc.eagle.query.service.SessionAnalysisSearchService;
import com.meituan.csc.aigc.eval.constants.CatConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 扶摇es rpc调用服务
 *
 * <AUTHOR>
 * @date 2025/5/22
 */
@Slf4j
@Component
public class PortalEagleServiceProxy {

    @Autowired
    private SessionAnalysisSearchService sessionAnalysisSearchService;

    private static final String LOG_PREFIX = "[PortalEagleServiceProxy]";

    /**
     * 天毫秒数
     */
    private static final Long ONE_DAY = 24 * 60 * 60 * 1000L;


    /**
     * 从portalSession获取会话信息
     *
     * @param sessionId 会话ID
     * @return ES会话查询结果
     */
    public SessionSearchResult getSessionInfoFromPortal(String sessionId) {
        SessionSearchRequest request = new SessionSearchRequest();
        request.setSessionId(sessionId);
        request.setPage(0);
        request.setSize(10);
        long nowTime = System.currentTimeMillis();
        request.setBeginTime(new Date(nowTime - ONE_DAY));
        request.setEndTime(new Date(nowTime));
        Transaction transaction = Cat.newTransaction(CatConstants.ANALYSIS_ES_RPC_TYPE, CatConstants.PORTAL_ES_SESSION);
        try {
            Response<PageDTO<SessionSearchResult>> response = sessionAnalysisSearchService.searchSession(request);
            if (null == response || !response.isSuccess() || null == response.getData() || CollectionUtils.isEmpty(response.getData().getContent())) {
                log.error("{}-getSessionInfoFromPortal failed, sessionId = {}, response = {}", LOG_PREFIX, sessionId, JSON.toJSONString(response));
                transaction.setStatus("-1");
                return null;
            }
            if (CollectionUtils.isEmpty(response.getData().getContent())) {
                return null;
            }
            return response.getData().getContent().get(0);
        } catch (Exception e) {
            log.error("{}-getSessionInfoFromPortal error, sessionId = {}, e:", LOG_PREFIX, sessionId, e);
            transaction.setStatus(e);
        } finally {
            transaction.complete();
        }
        return null;
    }

    /**
     * 从portalRobotInvokeLog获取标问信息
     *
     * @param sessionId 会话ID
     * @return ES查询标问信息结果
     */
    public RobotInvokeLogSearchResult getRobotInfoFromPortalRobot(String sessionId) {
        RobotInvokeLogSearchRequest request = new RobotInvokeLogSearchRequest();
        request.setSessionId(Long.valueOf(sessionId));
        request.setAddTime(new Date(System.currentTimeMillis() - ONE_DAY));
        request.setLastMaxTime(new Date(0L));
        request.setSize(100);
        Transaction transaction = Cat.newTransaction(CatConstants.ANALYSIS_ES_RPC_TYPE, CatConstants.PORTAL_ES_ROBOT);
        try {
            Response<RobotInvokeLogSearchResult> response = sessionAnalysisSearchService.getRobotInvokeLogV2(request);
            if (null == response || !response.isSuccess()) {
                log.error("{}-getRobotInfoFromPortalRobot failed, sessionId = {}, response = {}", LOG_PREFIX, sessionId, JSON.toJSONString(response));
                transaction.setStatus("-1");
                return null;
            }
            return response.getData();
        } catch (Exception e) {
            log.error("{}-getRobotInfoFromPortalRobot error, sessionId = {}, e:", LOG_PREFIX, sessionId, e);
            transaction.setStatus(e);
        } finally {
            transaction.complete();
        }
        return null;
    }

    /**
     * 从portalRobotTaskLog获取task信息
     *
     * @param instanceId 实例ID
     * @return ES查询task信息结果
     */
    public List<TaskLogSearchResult> getTaskInfoFromPortalRobot(String instanceId) {
        TaskLogSearchRequest request = new TaskLogSearchRequest();
        request.setInstanceId(instanceId);
        request.setPage(0);
        request.setSize(100);
        long nowTime = System.currentTimeMillis();
        request.setBeginTime(new Date(nowTime - ONE_DAY));
        request.setEndTime(new Date(nowTime));
        Transaction transaction = Cat.newTransaction(CatConstants.ANALYSIS_ES_RPC_TYPE, CatConstants.PORTAL_ES_TASK);
        try {
            Response<PageDTO<TaskLogSearchResult>> response = sessionAnalysisSearchService.getTaskLog(request);
            if (null == response || !response.isSuccess() || null == response.getData()) {
                log.error("{}-getTaskInfoFromPortalRobot failed, instanceId = {}, response = {}", LOG_PREFIX, instanceId, JSON.toJSONString(response));
                transaction.setStatus("-1");
                return null;
            }
            return response.getData().getContent();
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("{}-getTaskInfoFromPortalRobot error, instanceId = {}, e:", LOG_PREFIX, instanceId, e);
        } finally {
            transaction.complete();
        }
        return null;
    }
}
