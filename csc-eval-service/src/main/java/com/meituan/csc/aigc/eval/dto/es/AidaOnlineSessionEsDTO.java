package com.meituan.csc.aigc.eval.dto.es;

import com.meituan.csc.aigc.eval.enums.es.online.ChatRequestEnum;
import com.meituan.csc.aigc.eval.enums.es.online.EvaluationStarEnum;
import com.meituan.csc.aigc.eval.enums.es.online.QuestionSolveEnum;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 会话分析ES索引DTO
 *
 * <AUTHOR>
 * @date 2025/4/29
 */
@Data
public class AidaOnlineSessionEsDTO {
    
    /* 基础信息 */
    /**
     * 场景
     */
    private List<String> bizSceneId;
    /**
     * 应用ID列表
     */
    private List<AidaAppInfo> aidaAppInfo;
    /**
     * 业务
     */
    private String bu;
    /**
     * 子业务
     */
    private String subBu;
    /**
     * 会话ID
     */
    private String sessionId;
    /**
     * 访问ID列表
     */
    private List<String> visitIdList;
    /**
     * 渠道列表
     */
    private List<String> channelList;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 用户类型
     */
    private String userType;
    /**
     * 进线时订单ID
     */
    private String orderId;
    /**
     * session全量订单ID
     */
    private List<String> orderIdList;
    /**
     * 进线时间
     */
    private Date addTime;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /* 会话信息 */
    /**
     * 标问ID列表
     */
    private List<String> typicalQuestionIdList;
    /**
     * 满意度
     * @see EvaluationStarEnum
     */
    private String stars;
    /**
     * 消息类型，区分转人工
     * @see ChatRequestEnum
     */
    private List<String> typeList;
    /**
     * 消息发送类型，区分转人工
     */
    private List<String> senderTypeList;
    /**
     * 问题是否解决
     * @see QuestionSolveEnum
     */
    private String sessionSolved;
    /**
     * task异常退出
     */
    private String isExceptionEnd;
    /**
     * task超时退出
     */
    private String isTimeoutEnd;
    /**
     * 虚拟客服会话ID
     */
    private String hostServiceId;

    /* 执行记录 */
    /**
     * 机器人调用日记录
     **/
    private List<RobotInvokeLog> robotInvokeLogs;
    /**
     * task执行记录
     **/
    private List<TaskTraceLog> taskTraceLogs;
    /**
     * 对话消息记录
     */
    private List<DialogMessage> messages;

    /**
     * 对话消息
     */
    @Data
    public static class DialogMessage {
        /**
         * 消息ID
         */
        private Long messageId;
        /**
         * 消息内容
         */
        private String message;
        /**
         * 消息类型
         */
        private String type;
        /**
         * 发送类型
         */
        private String senderType;
        /**
         * 角色
         */
        private String role;
        /**
         * 时间
         */
        private Date time;
        /**
         * 转人工
         */
        private String transferStaff;
    }

    /**
     * AI搭应用信息
     */
    @Data
    public static class AidaAppInfo {
        /**
         * 空间ID
         */
        private String spaceId;
        /**
         * 应用ID
         */
        private String appId;
        /**
         * 版本ID
         */
        private String versionId;

    }
    
    /**
     * 机器人调用记录
     */
    @Data
    public static class RobotInvokeLog {
        /**
         * 触发时间
         */
        private Date triggerTime;
        /**
         * 触发时间
         */
        private String triggerName;
        /**
         * 用户query
         */
        private String userQuery;
        /**
         * 标问ID
         */
        private String typicalQuestionId;
        /**
         * 标问名称
         */
        private String typicalQuestionName;
    }
    
    /**
     * task执行记录
     */
    @Data
    public static class TaskTraceLog {
        /**
         * 触发时间
         */
        private String triggerTime;
        /**
         * task key
         */
        private String taskKey;
        /**
         * task名称
         */
        private String taskName;
        /**
         * task版本
         */
        private String taskVersion;
        /**
         * task节点ID list
         */
        private List<String> taskNodeIds;
        /**
         * task名称ID list
         */
        private List<String> taskNodeNames;
        /**
         * task实例ID
         */
        private String taskProcessInstanceId;
    }
}
