package com.meituan.csc.aigc.eval.param.task;

import com.meituan.csc.aigc.eval.dao.entity.EvalTaskQueryDetailPo;
import com.meituan.csc.aigc.eval.param.ConversationInfo;
import com.meituan.csc.aigc.eval.param.gpt.ChatGptHttpRequest;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class ApplicationRequestParam implements Serializable {
    private String inputContent;

    private Map<String, String> params;

    private String model;

    private Long evalTaskId;

    private Integer callType;

    private String ability;

    private ConversationInfo conversionInfo;

    private String robotId;

    private String creatorMis;

    private String apiSecretKey;

    private List<EvalTaskQueryDetailPo> queryDetailList;

    private String aidaAppId;

    private String sessionId;

    private Long datasetId;

    private Long queryId;

    private String testApplication;

    private List<ChatGptHttpRequest.GptMessage> messageList;

    private Boolean isInner;

    private Integer logType;

    private Long conversationId;

    /**
     * 大模型节点id
     */
    private String nodeId;

    private String nodeAppModelVersionId;

    /**
     * 节点id的调用密钥
     */
    private String nodeApiToken;

    /**
     * 是否开启流式输出
     */
    private Boolean appOpenStreaming;
    /**
     * 业务参数
     */
    private String businessParam;
}
