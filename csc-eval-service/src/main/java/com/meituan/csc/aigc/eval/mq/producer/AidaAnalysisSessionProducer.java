package com.meituan.csc.aigc.eval.mq.producer;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.meituan.csc.aigc.eval.dto.mq.producer.AidaAnalysisSessionDTO;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * AI搭会话分析MQ生产者
 *
 * <AUTHOR>
 * @date 2025/5/21
 */
@Component
@Slf4j
public class AidaAnalysisSessionProducer {

    @Autowired
    @Qualifier("caseAnalysisProducer")
    private IProducerProcessor<String, String> producer;

    private static final String LOG_PREFIX = "[AidaAnalysisSessionProducer]";

    /**
     * 推送延迟消息
     *
     * @param messageDTO 消息体
     * @param delayTime  延迟时间
     */
    public void sendDelayMessage(AidaAnalysisSessionDTO messageDTO, Long delayTime) {
        if (null == delayTime || delayTime < 5000L) {
            delayTime = 5000L;
        }
        try {
            ProducerResult result = producer.sendDelayMessage(JSON.toJSONString(messageDTO), delayTime);
            if (null == result || null == result.getProducerStatus() || !ProducerStatus.SEND_OK.equals(result.getProducerStatus())) {
                log.error("{}-sendDelayMessage fail, messageDTO = {}, result = {}", LOG_PREFIX, JSON.toJSONString(messageDTO), JSON.toJSONString((result)));
                Cat.logEvent("aidaAnalysisMessageProducer", "sendDelayMessageFail");
            }
        } catch (Exception e) {
            log.error("{}-sendDelayMessage error, messageDTO = {}, e:", LOG_PREFIX, JSON.toJSONString(messageDTO), e);
            Cat.logEvent("aidaAnalysisMessageProducer", "sendDelayMessageError");
        }
    }
}