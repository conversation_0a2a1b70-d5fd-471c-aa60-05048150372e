package com.meituan.csc.aigc.eval.enums.es;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/5/25
 */
@Getter
@AllArgsConstructor
public enum DialogRoleEnum {

    CUSTOMER(1, "user"),
    SYSTEM(2, "system"),
    STAFF(3, "staff"),
    OTHER(4, "other"),
    SMART_LLM(5, "smart_llm"),
    VIRTUAL_LLM(6, "virtual_llm");

    private final int code;
    private final String name;
}
