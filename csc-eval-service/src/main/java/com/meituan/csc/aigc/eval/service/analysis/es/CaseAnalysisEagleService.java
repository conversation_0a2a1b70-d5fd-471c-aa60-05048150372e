package com.meituan.csc.aigc.eval.service.analysis.es;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.SearchHits;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Map;

/**
 * 会话分析ES索引操作服务
 *
 * <AUTHOR>
 * @date 2025/4/29
 */
@Service
@Slf4j
public class CaseAnalysisEagleService {

    @Resource(name = "caseAnalysisClient")
    private RestHighLevelClient caseAnalysisClient;

    /**
     * 日志前缀
     */
    private static final String LOG_PREFIX = "[CaseAnalysisEagleService]";

    /**
     * 会话分析ES查询
     *
     * @param searchRequest 查询请求
     * @return SearchHits 查询结果
     */
    public SearchHits search(SearchRequest searchRequest) {
        try {
            SearchResponse searchResponse = caseAnalysisClient.search(searchRequest, RequestOptions.DEFAULT);
            if (searchResponse.status() != RestStatus.OK) {
                log.error("{}-search failed, status: {}, request: {}", LOG_PREFIX, searchResponse.status(), JSON.toJSONString(searchRequest));
                return null;
            }
            return searchResponse.getHits();
        } catch (Exception e) {
            log.error("{}-search error, request: {}, e:", LOG_PREFIX, JSON.toJSONString(searchRequest), e);
            return null;
        }
    }

    /**
     * 会话分析ES按ID查询
     *
     * @param getRequest 查询请求
     * @return 查询结果
     */
    public String get(GetRequest getRequest) {
        try {
            GetResponse getResponse = caseAnalysisClient.get(getRequest, RequestOptions.DEFAULT);
            if (getResponse.isExists()) {
                return getResponse.getSourceAsString();
            }
            return null;
        } catch (Exception e) {
            log.error("{}-get error, request: {}, e:", LOG_PREFIX, JSON.toJSONString(getRequest), e);
            return null;
        }
    }

    /**
     * 会话分析ES文档更新
     *
     * @param index    索引
     * @param id       文档ID
     * @param document 文档内容
     */
    public void upsert(String index, String id, Object document) {
        try {
            String documentStr = JSON.toJSONString(document);
            UpdateRequest updateRequest = new UpdateRequest(index, id);
            updateRequest.doc(documentStr, XContentType.JSON);
            // 数据不存在 执行插入
            updateRequest.docAsUpsert(true);
            caseAnalysisClient.update(updateRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("{}-upsert error, index: {}, id: {}, document: {}, e:", LOG_PREFIX, index, id, JSON.toJSONString(document), e);
        }
    }

    /**
     * 会话分析ES文档脚本更新
     *
     * @param index  索引
     * @param id     文档ID
     * @param script 脚本
     * @param params 参数
     */
    public void upsertByScript(String index, String id, String script, Map<String, Object> params) {
        try {
            Script upsertScript = new Script(ScriptType.INLINE, Script.DEFAULT_SCRIPT_LANG, script, params);
            UpdateRequest updateRequest = new UpdateRequest(index, id);
            updateRequest.scriptedUpsert(true);
            updateRequest.upsert("{}", XContentType.JSON);
            updateRequest.script(upsertScript);
            // 版本冲突重试次数
            updateRequest.retryOnConflict(5);
            caseAnalysisClient.update(updateRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("{}-upsertByScript error, index: {}, id: {}, e:", LOG_PREFIX, index, id, e);
        }
    }
}
