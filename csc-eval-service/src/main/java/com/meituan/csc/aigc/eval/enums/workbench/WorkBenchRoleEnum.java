package com.meituan.csc.aigc.eval.enums.workbench;

import lombok.Getter;

/**
 * 工作台角色枚举
 *
 * <AUTHOR>
 */
@Getter
public enum WorkBenchRoleEnum {
    USER(1, "user", "用户"),
    INSPECTOR(2, "inspector", "质检员");

    private final Integer code;
    private final String name;
    private final String description;

    WorkBenchRoleEnum(Integer code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    /**
     * 根据Code获取枚举<br>
     * Code不存在时默认返回USER
     *
     * @param code 角色Code
     * @return 角色枚举
     */
    public static WorkBenchRoleEnum getByCode(Integer code) {
        for (WorkBenchRoleEnum roleEnum : WorkBenchRoleEnum.values()) {
            if (roleEnum.getCode().equals(code)) {
                return roleEnum;
            }
        }
        return USER;
    }

}
