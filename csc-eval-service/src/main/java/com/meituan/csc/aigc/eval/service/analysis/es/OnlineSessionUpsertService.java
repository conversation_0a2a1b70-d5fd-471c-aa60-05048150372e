package com.meituan.csc.aigc.eval.service.analysis.es;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.csc.aigc.eval.constants.CatConstants;
import com.meituan.csc.aigc.eval.constants.EsScriptConstants;
import com.meituan.csc.aigc.eval.constants.LionConstants;
import com.meituan.csc.aigc.eval.dao.entity.AidaMessagesPo;
import com.meituan.csc.aigc.eval.dto.es.AidaOnlineSessionCacheDTO;
import com.meituan.csc.aigc.eval.dto.es.AidaOnlineSessionEsDTO;
import com.meituan.csc.aigc.eval.dto.mq.producer.AidaAnalysisSessionDTO;
import com.meituan.csc.aigc.eval.enums.es.mq.AnalysisMessageTypeEnum;
import com.meituan.csc.aigc.eval.enums.workbench.ChannelEnum;
import com.meituan.csc.aigc.eval.mq.producer.AidaAnalysisSessionProducer;
import com.meituan.csc.aigc.eval.service.analysis.OnlineSessionRedisService;
import com.meituan.csc.aigc.eval.service.analysis.cache.LocalCacheService;
import com.meituan.csc.aigc.eval.utils.EagleUtil;
import com.meituan.inf.xmdlog.ConfigUtil;
import com.sankuai.csccratos.aida.config.client.rpc.thrift.AidaInfoService;
import com.sankuai.csccratos.csc.aida.label.client.api.AidaSceneRemoteService;
import com.sankuai.csccratos.csc.aida.label.client.dto.common.AidaResponse;
import com.sankuai.csccratos.csc.aida.label.client.dto.scene.AidaSceneInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.meituan.csc.aigc.eval.constants.ExtraInfoConstants.*;

/**
 * 在线会话ES操作服务
 *
 * <AUTHOR>
 * @date 2025/4/30
 */
@Service
@Slf4j
public class OnlineSessionUpsertService {

    @Autowired
    private CaseAnalysisEagleService eagleService;

    @Autowired
    private OnlineSessionRedisService redisService;

    @Autowired
    private AidaSceneRemoteService aidaSceneRemoteService;

    @Autowired
    private AidaInfoService.Iface aidaInfoService;

    @Autowired
    private AidaAnalysisSessionProducer aidaAnalysisSessionProducer;

    @Autowired
    private LocalCacheService localCacheService;

    /**
     * 日志前缀
     */
    private static final String LOG_PREFIX = "[OnlineSessionService]";

    /**
     * 会话分析在线会话索引
     */
    private static final String ANALYSIS_ONLINE_SESSION_INDEX = "aida_session_online_";

    /**
     * 保存在线会话ES文档
     *
     * @param aidaMessages AI搭消息
     * @param extraInfo    AI搭扩展字段
     * @return 是否需要重复消费
     */
    public boolean upsertOnlineSession(AidaMessagesPo aidaMessages, JSONObject extraInfo) {
        String sessionId = aidaMessages.getSessionId();
        Transaction transaction = Cat.newTransaction(CatConstants.AIDA_ONLINE_SESSION_TYPE, CatConstants.AIDA_ONLINE_SESSION_UPSERT);
        try {
            if (!redisService.getSessionLock(sessionId)) {
                log.error("{}-upsertOnlineSession获取锁失败, sessionId = {}", LOG_PREFIX, sessionId);
                transaction.setStatus("lock fail");
                return true;
            }
            try {
                AidaOnlineSessionCacheDTO cacheDTO = redisService.getSessionInfo(sessionId);
                if (null == cacheDTO) {
                    // 新建session文档
                    AidaOnlineSessionEsDTO sessionDTO = createNewSession(aidaMessages, extraInfo, sessionId);
                    // 缓存关键信息
                    cacheDTO = buildSessionCache(sessionDTO, extraInfo);
                    redisService.setSessionInfo(sessionId, cacheDTO);
                    // 发送延迟MQ, 延迟同步扶摇数据
                    sendDelayDataSyncMq(sessionDTO);
                } else {
                    boolean isUpdate = updateExistingSession(aidaMessages, extraInfo, sessionId, cacheDTO);
                    if (isUpdate) {
                        // 增量数据缓存
                        redisService.setSessionInfo(sessionId, cacheDTO);
                    }
                }
            } catch (Exception e) {
                log.error("{}-upsertOnlineSession error, sessionId = {}, e:", LOG_PREFIX, sessionId, e);
                transaction.setStatus(e);
            } finally {
                redisService.deleteSessionLock(sessionId);
            }
        } finally {
            transaction.complete();
        }
        return false;
    }

    /**
     * 发送数据同步延迟MQ
     *
     * @param sessionDTO 会话文档
     */
    private void sendDelayDataSyncMq(AidaOnlineSessionEsDTO sessionDTO) {
        AidaAnalysisSessionDTO aidaAnalysisSessionDTO = new AidaAnalysisSessionDTO();
        aidaAnalysisSessionDTO.setSessionId(sessionDTO.getSessionId());
        aidaAnalysisSessionDTO.setCreateTime(sessionDTO.getCreateTime());
        aidaAnalysisSessionDTO.setChannel(ChannelEnum.ONLINE.getCode());
        // task、robot、session数据补全
        aidaAnalysisSessionDTO.setType(AnalysisMessageTypeEnum.DATA.getCode());
        Long delayTime = Lion.getLong(ConfigUtil.getAppkey(), LionConstants.CASE_ANALYSIS_SESSION_DATA_SYNC_DELAY_TIME, 600000L);
        aidaAnalysisSessionProducer.sendDelayMessage(aidaAnalysisSessionDTO, delayTime);
        // 会话记录补全
        aidaAnalysisSessionDTO.setType(AnalysisMessageTypeEnum.DIALOG.getCode());
        delayTime = Lion.getLong(ConfigUtil.getAppkey(), LionConstants.CASE_ANALYSIS_SESSION_DIALOG_SYNC_DELAY_TIME, 3600000L);
        aidaAnalysisSessionProducer.sendDelayMessage(aidaAnalysisSessionDTO, delayTime);
    }

    /**
     * 构建session缓存
     *
     * @param sessionDTO sessionES文档
     */
    private AidaOnlineSessionCacheDTO buildSessionCache(AidaOnlineSessionEsDTO sessionDTO, JSONObject extraInfo) {
        AidaOnlineSessionCacheDTO cacheDTO = new AidaOnlineSessionCacheDTO();
        cacheDTO.setSessionIndex(EagleUtil.getIndexByDate(ANALYSIS_ONLINE_SESSION_INDEX, sessionDTO.getAddTime()));
        cacheDTO.setVisitIdList(Optional.ofNullable(sessionDTO.getVisitIdList()).orElse(Lists.newArrayList()));
        cacheDTO.setChannelList(Optional.ofNullable(sessionDTO.getChannelList()).orElse(Lists.newArrayList()));
        cacheDTO.setSceneIdList(Optional.ofNullable(sessionDTO.getBizSceneId()).orElse(Lists.newArrayList()));
        // task实例ID
        String taskProcessInstanceId = extraInfo.getString(TASK_PROCESS_INSTANCE_ID);
        if (StringUtils.isNotBlank(taskProcessInstanceId) && !EMPTY_INSTANCE_ID.equals(taskProcessInstanceId)) {
            cacheDTO.setInstanceIdList(initParamList(taskProcessInstanceId));
        } else {
            cacheDTO.setInstanceIdList(Lists.newArrayList());
        }
        // 应用信息
        if (CollectionUtils.isNotEmpty(sessionDTO.getAidaAppInfo())) {
            List<AidaOnlineSessionCacheDTO.AidaAppInfo> appInfoList = sessionDTO.getAidaAppInfo().stream()
                    .map(this::convertToAidaAppInfo)
                    .collect(Collectors.toList());
            cacheDTO.setAppList(appInfoList);
        } else {
            cacheDTO.setAppList(Lists.newArrayList());
        }
        return cacheDTO;
    }

    /**
     * AI搭应用信息转换
     *
     * @param aidaAppInfo ES AI搭应用信息
     * @return 缓存AI搭应用信息
     */
    private AidaOnlineSessionCacheDTO.AidaAppInfo convertToAidaAppInfo(AidaOnlineSessionEsDTO.AidaAppInfo aidaAppInfo) {
        AidaOnlineSessionCacheDTO.AidaAppInfo cache = new AidaOnlineSessionCacheDTO.AidaAppInfo();
        cache.setSpaceId(aidaAppInfo.getSpaceId());
        cache.setAppId(aidaAppInfo.getAppId());
        cache.setVersionId(aidaAppInfo.getVersionId());
        return cache;
    }

    /**
     * 创建新的会话文档
     *
     * @param aidaMessages AI搭消息
     * @param extraInfo    扩展参数
     * @param sessionId    会话ID
     * @return 会话文档
     */
    private AidaOnlineSessionEsDTO createNewSession(AidaMessagesPo aidaMessages, JSONObject extraInfo, String sessionId) {
        AidaOnlineSessionEsDTO sessionDTO = new AidaOnlineSessionEsDTO();
        // 填充基本信息
        fillBasicInfo(sessionDTO, aidaMessages, extraInfo);

        Date now = new Date();
        sessionDTO.setAddTime(aidaMessages.getCreatedAt());
        sessionDTO.setCreateTime(now);
        sessionDTO.setUpdateTime(now);
        String index = EagleUtil.getIndexByDate(ANALYSIS_ONLINE_SESSION_INDEX, sessionDTO.getAddTime());
        // 插入新文档
        eagleService.upsert(index, sessionId, sessionDTO);
        return sessionDTO;
    }

    /**
     * 更新已存在的会话文档
     *
     * @param aidaMessages AI搭消息
     * @param extraInfo    扩展信息
     * @param sessionId    会话ID
     * @param cacheDTO     会话缓存
     * @return 是否更新
     */
    private boolean updateExistingSession(AidaMessagesPo aidaMessages, JSONObject extraInfo, String sessionId, AidaOnlineSessionCacheDTO cacheDTO) {
        boolean isUpdate = false;
        AidaSceneInfoDTO sceneInfoDTO = listSceneByAppId(aidaMessages.getAppId());
        String channel = extraInfo.getString(CHANNEL);
        String taskInstanceId = extraInfo.getString(TASK_PROCESS_INSTANCE_ID);
        String script = "";
        Map<String, Object> paramMap = Maps.newHashMap();
        // aida app
        String appId = aidaMessages.getAppId();
        String spaceId = getTenantIdFromCache(appId);
        String versionId = getVersionIdByMessageId(aidaMessages.getId());
        boolean hasNewApp = false;
        if (StringUtils.isNotBlank(appId) && StringUtils.isNotBlank(spaceId) && StringUtils.isNotBlank(versionId)) {
            List<AidaOnlineSessionCacheDTO.AidaAppInfo> appInfoList = cacheDTO.getAppList();
            if (appInfoList.isEmpty()) {
                hasNewApp = true;
            } else {
                boolean hasSameApp = appInfoList.stream().anyMatch(aidaAppInfo ->
                        Objects.equals(aidaAppInfo.getAppId(), appId)
                                && Objects.equals(aidaAppInfo.getSpaceId(), spaceId)
                                && Objects.equals(aidaAppInfo.getVersionId(), versionId)
                );
                hasNewApp = !hasSameApp;
            }
        }
        if (hasNewApp) {
            script += EsScriptConstants.AIDA_APP_LIST_ADD;
            paramMap.put(EsScriptConstants.AIDA_APP_ID_PARAM, appId);
            paramMap.put(EsScriptConstants.AIDA_VERSION_ID_PARAM, versionId);
            paramMap.put(EsScriptConstants.AIDA_SPACE_ID_PARAM, spaceId);

            AidaOnlineSessionCacheDTO.AidaAppInfo aidaAppInfo = new AidaOnlineSessionCacheDTO.AidaAppInfo();
            aidaAppInfo.setAppId(appId);
            aidaAppInfo.setSpaceId(spaceId);
            aidaAppInfo.setVersionId(versionId);
            cacheDTO.getAppList().add(aidaAppInfo);
        }
        // channel
        if (StringUtils.isNotBlank(channel) && (CollectionUtils.isEmpty(cacheDTO.getChannelList()) || !cacheDTO.getChannelList().contains(channel))) {
                script += EsScriptConstants.CHANNEL_LIST_ADD;
                paramMap.put(EsScriptConstants.CHANNEL_ID_PARAM, channel);
                cacheDTO.getChannelList().add(channel);
            }
        // 场景
        if (null != sceneInfoDTO && null != sceneInfoDTO.getSceneId()
                && (CollectionUtils.isEmpty(cacheDTO.getSceneIdList()) || !cacheDTO.getSceneIdList().contains(sceneInfoDTO.getSceneId().toString()))) {
            String sceneId = sceneInfoDTO.getSceneId().toString();
            script += EsScriptConstants.SCENE_ID_LIST_ADD;
            paramMap.put(EsScriptConstants.SCENE_ID_PARAM, sceneId);
            cacheDTO.getSceneIdList().add(sceneId);
        }
        // task instance
        if (StringUtils.isNotBlank(taskInstanceId) && !Objects.equals(taskInstanceId, EMPTY_INSTANCE_ID) &&
                (CollectionUtils.isEmpty(cacheDTO.getInstanceIdList()) || !cacheDTO.getInstanceIdList().contains(taskInstanceId))) {
            // 不更新es，记录新的instanceId
            cacheDTO.getInstanceIdList().add(taskInstanceId);
            isUpdate = true;
        }
        if (StringUtils.isBlank(script)) {
            return isUpdate;
        }
        String index = cacheDTO.getSessionIndex();
        eagleService.upsertByScript(index, sessionId, script, paramMap);
        return true;
    }

    /**
     * 填充会话基础字段
     *
     * @param sessionDTO   会话文档
     * @param aidaMessages AI搭消息
     * @param extraInfo    扩展信息
     */
    private void fillBasicInfo(AidaOnlineSessionEsDTO sessionDTO, AidaMessagesPo aidaMessages, JSONObject extraInfo) {
        // 场景
        AidaSceneInfoDTO sceneInfoDTO = listSceneByAppId(aidaMessages.getAppId());
        if (null != sceneInfoDTO && null != sceneInfoDTO.getSceneId()) {
            sessionDTO.setBizSceneId(initParamList(String.valueOf(sceneInfoDTO.getSceneId())));
        }
        sessionDTO.setSessionId(aidaMessages.getSessionId());
        sessionDTO.setBu(extraInfo.getString(BU));
        sessionDTO.setSubBu(extraInfo.getString(SUB_BU));
        sessionDTO.setUserId(aidaMessages.getUserId());
        sessionDTO.setUserType(aidaMessages.getUserType());
        sessionDTO.setHostServiceId(extraInfo.getString(HOST_SERVICE_ID));
        sessionDTO.setChannelList(initParamList(extraInfo.getString(CHANNEL)));
        sessionDTO.setVisitIdList(initParamList(extraInfo.getString(VISIT_ID)));
        // 应用
        String appId = aidaMessages.getAppId();
        String spaceId = getTenantIdFromCache(appId);
        String versionId = getVersionIdByMessageId(aidaMessages.getId());
        if (StringUtils.isNotBlank(appId) && StringUtils.isNotBlank(spaceId) && StringUtils.isNotBlank(versionId)) {
            AidaOnlineSessionEsDTO.AidaAppInfo aidaAppInfo = new AidaOnlineSessionEsDTO.AidaAppInfo();
            aidaAppInfo.setAppId(appId);
            aidaAppInfo.setSpaceId(spaceId);
            aidaAppInfo.setVersionId(versionId);
            sessionDTO.setAidaAppInfo(Lists.newArrayList(aidaAppInfo));
        }
    }

    /**
     * 初始化列表
     *
     * @param param 参数字段
     * @return 字段列表
     */
    private List<String> initParamList(String param) {
        List<String> paramList = Lists.newArrayList();
        if (StringUtils.isBlank(param)) {
            return paramList;
        }
        paramList.add(param);
        return paramList;
    }

    /**
     * 从缓存获取空间ID
     *
     * @param appId 应用ID
     * @return 空间ID
     */
    private String getTenantIdFromCache(String appId) {
        String tenantId = localCacheService.getTenantId(appId);
        if (StringUtils.isBlank(tenantId)) {
            tenantId = getTenantIdByAppId(appId);
            localCacheService.setTenantId(appId, tenantId);
        }
        return tenantId;
    }

    /**
     * 根据应用ID获取空间ID
     *
     * @param appId 应用ID
     * @return 空间ID
     */
    public String getTenantIdByAppId(String appId) {
        Transaction transaction = Cat.newTransaction(CatConstants.ANALYSIS_ES_RPC_TYPE, CatConstants.AIDA_RUNTIME_TENANT_ID);
        try {
            return aidaInfoService.getTenantIdByAppId(appId);
        } catch (Exception e) {
            log.error("{}-getTenantIdByAppId error, appId = {}, e:", LOG_PREFIX, appId, e);
            transaction.setStatus(e);
        } finally {
            transaction.complete();
        }
        return null;
    }

    /**
     * 根据消息ID获取版本ID
     *
     * @param messageId 消息ID
     * @return 版本ID
     */
    public String getVersionIdByMessageId(String messageId) {
        Transaction transaction = Cat.newTransaction(CatConstants.ANALYSIS_ES_RPC_TYPE, CatConstants.AIDA_RUNTIME_VERSION_ID);
        try {
            return aidaInfoService.getVersionIdByMessageId(messageId);
        } catch (Exception e) {
            log.error("{}-getVersionIdByMessageId error, messageId = {}, e:", LOG_PREFIX, messageId, e);
            transaction.setStatus(e);
        } finally {
            transaction.complete();
        }
        return null;
    }

    /**
     * 根据应用ID获取对应场景信息
     *
     * @param appId 应用ID
     * @return ES查询标问信息结果
     */
    public AidaSceneInfoDTO listSceneByAppId(String appId) {
        Transaction transaction = Cat.newTransaction(CatConstants.ANALYSIS_ES_RPC_TYPE, CatConstants.AIDA_LABEL_SCENE);
        try {
            AidaResponse<AidaSceneInfoDTO> response = aidaSceneRemoteService.getSceneInfoByAppId(appId);
            if (null == response) {
                log.error("{}-listSceneByAppId failed, appId = {}", LOG_PREFIX, appId);
                transaction.setStatus("fail");
                return null;
            }
            return response.getData();
        } catch (Exception e) {
            log.error("{}-listSceneByAppId error, appId = {}, e:", LOG_PREFIX, appId, e);
            transaction.setStatus(e);
        } finally {
            transaction.complete();
        }
        return null;
    }

}
