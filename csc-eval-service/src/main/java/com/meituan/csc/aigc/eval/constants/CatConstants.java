package com.meituan.csc.aigc.eval.constants;

/**
 * <AUTHOR>
 */
public class CatConstants {

    public static final String S3_UPLOAD_FILE_ERROR = "s3.upload.file.error";

    public static final String LLM_CATEGORY = "llm.category";

    public static final String TASK_PARALLEL_LOCK_EXCEPTION = "task.parallel.lock.exception";
    public static final String AUTO_INSPECT_TASK_LOCK_EXCEPTION = "auto.inspect.task.lock.exception";
    public static final String TRAIN_DATASET_VERSION_LOCK_EXCEPTION = "train.dataset.version.lock.exception";


    /***********************可忽略异常 start******************************/

    public static final String IGNORE_EXCEPTION_EVENT = "ignore.exception";


    public static final String CORE_EXCEPTION = "cors.exception";


    public static final String PARSE_EXCEPTION = "parse.exception";

    public static final String GPT_EXCEPTION = "gpt.exception";


    /***********************可忽略异常 end******************************/

    /**
     * 运营分析工具
     */
    public static final String WORKBENCH = "workbench";


    /**
     * 任务 Controller
     */
    public static final String TASK_CONTROLLER = "task.controller";

    /**
     * 自动任务执行
     */
    public static final String TASK_EXECUTE = "task.execute";

    /* 在线会话MQ */
    public static final String ONLINE_SESSION_MQ_TYPE = "online.session.mq";
    public static final String ONLINE_SESSION_NPS = "nps_core";
    public static final String ONLINE_SESSION_PORTAL_MESSAGE = "portal_message";
    public static final String ONLINE_SESSION_ROBOT_LOG = "robot_invoke_log";
    public static final String ONLINE_SESSION_TASK_LOG = "task_log";
    public static final String ONLINE_SESSION_CLIENT_LOG = "client_log_extra";
    public static final String ONLINE_SESSION_DATA_SYNC = "aida_analysis_session";

    /* 扶摇ES rpc */
    public static final String ANALYSIS_ES_RPC_TYPE = "analysis.es.rpc";
    public static final String PORTAL_ES_SESSION = "portal_es_session";
    public static final String PORTAL_ES_TASK = "portal_es_task";
    public static final String PORTAL_ES_ROBOT = "portal_es_robot";
    public static final String AIDA_LABEL_SCENE = "aida_label_scene";
    public static final String AIDA_RUNTIME_TENANT_ID = "aida_runtime_tenant_id";
    public static final String AIDA_RUNTIME_VERSION_ID = "aida_runtime_version_id";
    public static final String PORTAL_MESSAGE = "portal_message";

    /* AI搭在线ES */
    public static final String AIDA_ONLINE_SESSION_TYPE = "aida.online.session.es";
    public static final String AIDA_ONLINE_SESSION_UPSERT = "aida_online_session_upsert";

    /**
     * 自动分析工具
     */
    public static final String AUTO_ANALYSIS_TOOL = "auto.analysis.tool";

}
