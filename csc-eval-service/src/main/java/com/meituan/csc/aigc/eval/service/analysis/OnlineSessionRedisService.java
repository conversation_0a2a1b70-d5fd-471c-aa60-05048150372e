package com.meituan.csc.aigc.eval.service.analysis;

import com.alibaba.fastjson.JSON;
import com.dianping.squirrel.client.StoreKey;
import com.meituan.csc.aigc.eval.dto.es.AidaOnlineSessionCacheDTO;
import com.meituan.csc.aigc.eval.proxy.RedisClientProxy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 在线会话缓存服务
 *
 * <AUTHOR>
 * @date 2025/5/6
 */
@Service
@Slf4j
public class OnlineSessionRedisService {

    @Autowired
    private RedisClientProxy redisStoreClient;

    /**
     * 日志前缀
     */
    private static final String LOG_PREFIX = "[OnlineSessionRedisService]";

    /**
     * 会话分析在线会话key
     */
    private static final String SESSION_CATEGORY = "analysis_online_session";
    /**
     * 会话分析在线会话锁key
     */
    private static final String SESSION_LOCK_CATEGORY = "analysis_online_session_lock";
    /**
     * 过期时间
     */
    private static final Integer EXPIRE_TIME = 24 * 60 * 60;

    /**
     * 缓存会话信息
     *
     * @param sessionId 会话ID
     * @param cacheDTO  会话缓存
     */
    public void setSessionInfo(String sessionId, AidaOnlineSessionCacheDTO cacheDTO) {
        StoreKey key = new StoreKey(SESSION_CATEGORY, sessionId);
        try {
            String sessionInfo = JSON.toJSONString(cacheDTO);
            redisStoreClient.set(key, sessionInfo, EXPIRE_TIME);
        } catch (Exception e) {
            log.error("{}-cacheAidaSessionInfo error, e:", LOG_PREFIX, e);
        }
    }

    /**
     * 获取会话缓存
     *
     * @param sessionId 会话ID
     * @return 会话缓存
     */
    public AidaOnlineSessionCacheDTO getSessionInfo(String sessionId) {
        StoreKey key = new StoreKey(SESSION_CATEGORY, sessionId);
        try {
            String value = redisStoreClient.get(key);
            if (StringUtils.isBlank(value)) {
                return null;
            }
            return JSON.parseObject(value, AidaOnlineSessionCacheDTO.class);
        } catch (Exception e) {
            log.error("{}-cacheAidaSessionInfo error, sessionId = {}, e:", LOG_PREFIX, sessionId, e);
        }
        return null;
    }

    /**
     * 获取会话锁
     *
     * @param sessionId 会话ID
     * @return 是否获取成功
     */
    public boolean getSessionLock(String sessionId) {
        StoreKey key = new StoreKey(SESSION_LOCK_CATEGORY, sessionId);
        int maxRetries = 3;
        int currentRetry = 0;

        while (currentRetry < maxRetries) {
            try {
                Boolean result = redisStoreClient.setnx(key, sessionId, 10);
                if (Boolean.TRUE.equals(result)) {
                    return true;
                }
                currentRetry++;
                if (currentRetry < maxRetries) {
                    Thread.sleep(100);
                }
            } catch (Exception e) {
                log.error("{}-getSessionLock error, sessionId = {}, retry = {}, e:", LOG_PREFIX, sessionId, currentRetry + 1, e);
                currentRetry++;
            }
        }
        return false;
    }

    /**
     * 删除会话锁
     *
     * @param sessionId 会话ID
     * @return 是否成功
     */
    public boolean deleteSessionLock(String sessionId) {
        try {
            StoreKey key = new StoreKey(SESSION_LOCK_CATEGORY, sessionId);
            Boolean result = redisStoreClient.delete(key);
            if (!Boolean.TRUE.equals(result)) {
                log.error("{}-deleteSessionLock fail, sessionId = {}, result = {}", LOG_PREFIX, sessionId, result);
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("{}-deleteSessionLock error, sessionId = {}, e:", LOG_PREFIX, sessionId);
        }
        return false;
    }
}
