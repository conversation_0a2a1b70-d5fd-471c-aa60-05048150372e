package com.meituan.csc.aigc.eval.enums.es;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
@AllArgsConstructor
public enum MessageTypeEnum {
    TEXT("text"),
    ORDER("order"),
    ORDER_CONFIRM("orderConfirm"),
    VOICE("voice"),
    FILE("file"),
    FAQ_ANSWER("faqAnswer"),
    OPTION("option"),
    COUPON_LIST_COMPONENT("CouponListComponent"),
    COUPON_LIST_CONFIRM("CouponListConfirm"),
    CHAT_CALL_OUT("ChatCallOut"),
    No_ORDER_LIST_COMPONENT("NoOrderListComponent"),
    NO_ORDER_LIST_COMPONENT_END("NoOrderListComponentEnd"),
    UPDATE_MESSAGE("UpdateMessage"),
    ORDER_LIST("orderList"),
    ORDER_SELECTOR("OrderSelector"),
    ORDER_SELECTOR_END("VisitorForm"),
    NO_ORDER_LIST_CONFIRM("NoOrderListConfirm");

    private final String value;

    public static MessageTypeEnum findByValue(String value) {
        return Arrays.stream(MessageTypeEnum.values()).filter(e -> Objects.equals(e.getValue(), value)).findFirst().orElse(null);
    }
}