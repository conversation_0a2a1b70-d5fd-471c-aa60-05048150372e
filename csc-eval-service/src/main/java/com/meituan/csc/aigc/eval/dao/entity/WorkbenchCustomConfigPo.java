package com.meituan.csc.aigc.eval.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;


/**
 * <p>
 * 工作台自定义配置类，存储与工作台相关的自定义配置项。
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("workbench_custom_config")
public class WorkbenchCustomConfigPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 配置唯一id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 配置名称
     */
    private String name;

    /**
     * AI搭空间标识
     */
    private String platformWorkspace;

    /**
     * ai搭应用ID
     */
    private String appId;

    /**
     * ai搭机器人id
     */
    private String appVersionId;

    /**
     * 自定义配置
     */
    private String config;

    /**
     * 配置状态 无效 有效
     */
    private String status;

    /**
     * 配置类型 信号全局排序signal_all_sort, 信号节点排序signal_node_sort, 默认展示节点 selected_node
     *
     * @see com.meituan.csc.aigc.eval.enums.workbench.CustomConfigTypeEnum
     */
    private String type;

    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 更新时间
     */
    private Date gmtModified;

    /**
     * 创建人
     */
    private String creatorMis;

    /**
     * 更新人
     */
    private String updaterMis;
}
