package com.meituan.csc.aigc.eval.param.dataset;

import com.meituan.csc.aigc.eval.dto.dataset.SingleTemplateFieldBindDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DatasetExtraParam implements Serializable {
    private ConditionConfig configCondition;
    private List<String> headList;
    private List<SingleTemplateFieldBindDTO> headMapList;
    private List<SingleTemplateFieldBindDTO> mapList;
    private List<String> customList;
}
