package com.meituan.csc.aigc.eval.service;

import com.meituan.csc.aigc.eval.dao.entity.AidaMessagesPo;
import com.meituan.csc.aigc.eval.dao.entity.MetricConfigPo;
import com.meituan.csc.aigc.eval.dto.PageData;
import com.meituan.csc.aigc.eval.dto.gpt.GptReplyDTO;
import com.meituan.csc.aigc.eval.dto.workbench.*;
import com.meituan.csc.aigc.eval.param.PageParam;
import com.meituan.csc.aigc.eval.param.customconfig.LlmSelectedParam;
import com.meituan.csc.aigc.eval.param.customconfig.NodeSelectedParam;
import com.meituan.csc.aigc.eval.param.customconfig.SignalReorderParam;
import com.meituan.csc.aigc.eval.param.inspect.InspectWorkbenchAgreeParam;
import com.meituan.csc.aigc.eval.param.inspect.InspectWorkbenchCollectParam;
import com.meituan.csc.aigc.eval.param.inspect.InspectWorkbenchDetailParam;
import com.meituan.csc.aigc.eval.param.inspect.InspectWorkbenchDetailPathParam;
import com.meituan.csc.aigc.eval.param.mark.AutoInspectionRequestParam;
import com.meituan.csc.aigc.eval.param.workbench.*;
import com.meituan.csc.aigc.runtime.inner.dto.InnerAppConfigDTO;
import com.sankuai.call.sdk.entity.record.QueryRecordDataDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface InspectWorkbenchExecuteService {

    Boolean deleteSessionFavorite(String sessionId);
    /**
     * 分页查询会话
     * 只走es，目前只有在线
     * @param condition 查询条件
     * @return 会话列表
     */
    PageData<InspectWorkbenchDTO> pageSessionNew(PageParam<InspectWorkbenchConditionParam> condition);

    /**
     * 分页查询会话
     *
     * @param condition 查询条件
     * @return 会话列表
     */
    PageData<InspectWorkbenchDTO> pageSession(PageParam<InspectWorkbenchConditionParam> condition);

    /**
     * 查询会话详情
     *
     * @param sessionId        会话ID
     * @param platformType     平台类型
     * @param llmSessionIdList 大模型会话ID列表
     * @return 会话详情
     */
    List<InspectWorkbenchSessionDetailDTO> sessionDetail(String sessionId, Integer platformType, List<String> llmSessionIdList);

    /**
     * 保存Session日志
     *
     * @param sessionId 会话ID
     */
    void saveLog(String sessionId);

    /**
     * 查询会话概览信息
     *
     * @param sessionId 会话ID
     * @return 会话基本信息
     */
    InspectWorkbenchDTO sessionOverview(String sessionId);

    /**
     * 查询会话维度基本信息
     *
     * @param sessionId 会话ID
     * @return 会话基本信息
     */
    InspectWorkbenchDTO sessionBasic(String sessionId, String analysisType);

    /**
     * 根据不同渠道 查询会话筛选条件
     *
     * @param analysisType
     * @return
     */
    FilterConfigDTO sessionConditionConfig(String analysisType);

    /**
     * 获取场景关系配置
     * @return
     */
    SceneRelationConfigDTO sessionConditionSceneRelationConfig();


    /**
     * 获取任务版本
     * @param taskKey
     * @return
     */
    TaskVersionDTO sessionConditionTaskKeyVersions(String taskKey);
    /**
     * 获取任务关键节点配置
     * @return
     */
    TaskVersionNodeDTO sessionConditionTaskKeyNodes(String taskKey, String taskVersion);

    /**
     * 添加常用的会话筛选条件为记录
     *
     * @param param
     * @return
     */
    boolean addSessionConditionRecord(SessionConditionRecordDTO param);

    /**
     * 根据不同渠道 查询会话筛选条件
     *
     * @param analysisType
     * @return
     */
    List<SessionConditionRecordDTO> listSessionConditionRecord(String analysisType);

    /**
     * 根据不同渠道 查询会话筛选条件
     *
     * @param param
     * @return
     */
    boolean deleteSessionConditionRecord(SessionConditionRecordDTO param);

    /**
     * 查询会话IVR信息
     *
     * @param sessionId
     * @return
     */
    String sessionIvrInfo(String sessionId);

    /**
     * 查询消息详情
     *
     * @param param 查询参数
     * @return 消息详情
     */
    InspectWorkbenchQueryDetailDTO queryDetail(InspectWorkbenchDetailParam param);

    /***
     * 查询模型调试信息 树形结构
     * @param param
     * @return
     */
    InspectWorkbenchQueryLlmTreeDTO queryLlmTree(InspectWorkbenchDetailParam param);

    /**
     * 查询消息的执行链路可视化相关信息
     *
     * @param param 查询参数
     * @return 消息详情
     */
    InspectWorkbenchQueryDetailPathDTO queryDetailPath(InspectWorkbenchDetailPathParam param);

    /**
     * 保存Query日志
     *
     * @param param Query查询参数
     */
    void saveLog(InspectWorkbenchDetailParam param);

    /**
     * 消息质检
     *
     * @param param 质检参数
     */
    void queryInspect(InspectWorkbenchAgreeParam param);

    /**
     * 加入错题集
     *
     * @param param 错题集参数
     */
    void queryCollect(InspectWorkbenchCollectParam param);

    /**
     * 查询人工质检
     *
     * @param dimension     质检维度 0-Session维度 1-Query维度
     * @param applicationId 应用ID
     * @param sessionId     会话ID
     * @param messageId     消息ID （可选）
     * @param llmMessageId  aida消息id
     * @return 质检结果
     */
    List<ManualInspectResultDTO> getManualInspectResult(Integer dimension, String applicationId, String sessionId, String messageId, String llmMessageId);

    /**
     * 人工质检提交
     *
     * @param param 质检结果
     */
    void submitManualInspectResult(ManualInspectParam param);

    /**
     * 采纳自动质检结果
     *
     * @param param 工作台采纳ID
     */
    void adoptAutoInspectResult(WorkbenchAdoptParam param);

    /**
     * 获取机器人工质检指标映射列表
     *
     * @return 指标映射DTO
     */
    List<WorkbenchMetricsMappingDTO> getWorkbenchMetricsMappingDTOList();

    /**
     * 校验质检请求参数
     *
     * @param param 质检工作台请求参数
     */
    void checkWorkbenchCommonParam(WorkbenchCommonParam param);

    /**
     * 获取自动指标ID列表
     *
     * @return 自动指标ID列表
     */
    List<Long> getAutoMetricId(String sessionId, String applicationId);

    /**
     * 获取指标配置Map
     *
     * @param metricIdList 指标ID列表
     * @return 指标配置映射关系，键为指标ID，值为指标配置信息
     */
    Map<Long, MetricConfigPo> getMetricConfigMap(List<Long> metricIdList);


    /**
     * 获取自动质检统计信息
     *
     * @return 自动质检统计信息列表
     */
    List<AutoInspectionStatisticsDTO> getAutoInspectionStatistics();


    /**
     * 根据会话ID获取场景类型名称
     *
     * @param sessionId 会话ID
     * @return 场景类型名称
     */
    String getSceneTypeNameBySessionId(String sessionId);


    /**
     * 获取 session 详细信息
     *
     * @param request 请求参数
     * @return 会话详细信息
     */
    WorkbenchAutoInspectDTO getSessionDetailInfo(AutoInspectionRequestParam request);

    /**
     * 批量获取 session 详细信息
     *
     * @param sessionIdList 会话ID列表
     * @return 会话详细信息列表
     */
    List<WorkbenchAutoInspectDTO> getSessionDetailInfoBatch(List<String> sessionIdList);

    /**
     * 获取工作空间列表
     *
     * @return 工作空间列表
     */
    List<WorkspaceAppDTO> getWorkspacesAndApps();

    /**
     * 根据场景类型获取工作空间列表
     */
    List<WorkspaceAppDTO> getWorkspacesAndAppsBySceneType(String sceneType);

    /**
     * 导出会话
     *
     * @param condition 导出条件
     */
    void exportSession(InspectWorkbenchConditionParam condition);

    /**
     * 获取用户角色
     *
     * @return 角色枚举
     */
    WorkbenchRoleDTO getRole();

    /**
     * 获取变量映射
     *
     * @return 工作空间列表
     */
    Map<String, String> getVariableMap(InspectWorkbenchQueryDetailDTO.Detail.AidaTrace log, Map<String, InnerAppConfigDTO> innerAppConfigMap, Boolean flag, String modelConfigVersionId);

    /**
     * 构建query/detail信息
     *
     * @param param 查询参数
     * @param messageList aida侧同一session的原生消息列表
     * @param matchMessage 匹配的消息,若没有传null
     * @return InspectWorkbenchQueryDetailDTO结构
     */
    InspectWorkbenchQueryDetailDTO buildAidaQueryDetailDTO(InspectWorkbenchDetailParam param, List<AidaMessagesPo> messageList, AidaMessagesPo matchMessage);

    /***
     * 变更信号排序接口
     * @param param
     */
    void signalReorder(SignalReorderParam param);

    /***
     * 变更节点选择接口
     * @param param
     * @return
     */
    void nodeSelected(NodeSelectedParam param);

    /***
     * 变更模型选择接口
     * @param param
     * @return
     */
    void llmSelected(LlmSelectedParam param);

    /***
     * 调试模型节点
     * @param param
     * @return
     */
    GptReplyDTO llmDebug(WorkbenchLlmDebugParam param);

    /**
     * 保存参考调试窗口宽度设置
     * @param totalWidth 总宽度
     * @param referenceWindowWidth 参考窗口宽度
     */
    void saveReferenceDebuggingWindowWidth(Integer totalWidth, Integer referenceWindowWidth);

    /**
     * 获取录音详情
     * @param contactId
     * @return
     */
    List<QueryRecordDataDTO> getCallOutInfo(String contactId);

    /**
     * 根据主流程appId与conversationId列表获取对应的messageDTO列表
     *
     * @param conversationIds 会话ID列表
     * @return 返回对应的messageDTO列表
     */
//    List<MessagesDTO> listMessageByConvIdsAndMainAppId(List<String> conversationIds);
}
