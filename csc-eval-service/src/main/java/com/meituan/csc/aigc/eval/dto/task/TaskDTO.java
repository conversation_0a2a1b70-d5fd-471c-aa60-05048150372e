package com.meituan.csc.aigc.eval.dto.task;

import com.meituan.csc.aigc.eval.dto.application.ApplicationOutputDTO;
import com.meituan.csc.aigc.eval.dto.dataset.DatasetDTO;
import com.meituan.csc.aigc.eval.dto.dataset.TemplateFieldBindDTO;
import com.meituan.csc.aigc.eval.dto.metric.TaskMetricInfoDTO;
import com.meituan.csc.aigc.eval.param.application.ApplicationResultParam;
import com.meituan.csc.aigc.eval.param.task.AidaModelConfig;
import com.meituan.csc.aigc.eval.param.task.ScoreThresholdParam;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class TaskDTO implements Serializable {
    /**
     * 任务id
     */
    private Long id;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 能力类型
     */
    private Integer abilityType;

    /**
     * 评测集名称，以逗号分隔
     */
    private String dataSetName;

    /**
     * 数据集列表
     */
    private List<DatasetDTO> datasetList;

    /**
     * 模型名称，以逗号分隔
     */
    private String modelName;

    /**
     * 任务执行状态 0-创建中 1-排队中 2-评测中 3-已完成 4-执行失败
     */
    private Integer status;

    /**
     * 任务执行进度
     */
    private String progress;

    /**
     * 评测标准
     */
    private String standard;

    /**
     * 创建人
     */
    private String creatorMis;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    private Integer modelSource;

    private String scene;

    private Integer callType;
    private List<String> inspectors;

    private String scoreThreshold;

    private List<TaskMetricInfoDTO> metricList;

    /**
     * 质检进度
     */
    private String inspectProgress;

    /**
     * 抽样比例
     */
    private String samplingRatio;

    /**
     * 当前用户是否可以质检
     */
    private Boolean canInspect;

    private Integer score;

    private String version;

    private List<ApplicationOutputDTO> outputKeyMap;

    /**
     * 用户输入来源
     */
    private Integer inputSource;

    /**
     * 对话历史来源
     */
    private Integer historySource;
    /**
     * 字段映射关系
     */
    private Map<Long, List<TemplateFieldBindDTO>> bindFields;


    /**
     * 模拟次数
     */
    private String mockTimes;
    /**
     * 是否评测任务
     */
    private Boolean whetherRegressDataset;

    /**
     * 数据集id
     */
    private List<Integer> datasetIdList;

    /**
     * 相似度阈值
     */
    private List<ScoreThresholdParam> scoreThresholdList;

    /**
     * 话术字段
     */
    private List<ApplicationResultParam> modelResultParamList;

    /**
     * 模型输出结果结构化解析
     */
    private List<ApplicationResultParam> resultParamList;

    /**
     * ai搭配置，模型来源为ai搭时填写
     */
    private List<AidaModelConfig> aidaModelConfig;

    private Integer metricType;


    /**
     * 应用配置，模型来源为系统时填写
     */
    private List<Long> applicationConfig;
}
