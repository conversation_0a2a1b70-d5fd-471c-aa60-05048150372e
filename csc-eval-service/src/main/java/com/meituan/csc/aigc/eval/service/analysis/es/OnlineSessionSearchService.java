package com.meituan.csc.aigc.eval.service.analysis.es;

import com.alibaba.fastjson.JSON;
import com.dianping.csc.center.dto.question.TypicalQuestionDTO;
import com.dianping.csc.center.dto.question.TypicalQuestionRobotKnowledgeDTO;
import com.dianping.csc.center.query.QueryResponse;
import com.dianping.csc.center.query.question.TypicalQuestionQueryDTO;
import com.dianping.csc.center.service.backend.question.TypicalQuestionBService;
import com.dianping.csc.center.service.backend.question.TypicalQuestionRobotKnowledgeBService;
import com.google.common.collect.Lists;
import com.meituan.csc.aigc.eval.constants.EsFieldConstants;
import com.meituan.csc.aigc.eval.dto.PageData;
import com.meituan.csc.aigc.eval.dto.es.AidaOnlineSessionEsDTO;
import com.meituan.csc.aigc.eval.dto.es.OnlineSessionSearchDTO;
import com.meituan.csc.aigc.eval.enums.es.DialogRoleEnum;
import com.meituan.csc.aigc.eval.enums.es.online.*;
import com.meituan.csc.aigc.eval.utils.EagleUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 在线会话ES查询服务
 *
 * <AUTHOR>
 * @date 2025/5/7
 */
@Service
@Slf4j
public class OnlineSessionSearchService {

    @Autowired
    private CaseAnalysisEagleService eagleService;

    @Autowired
    private TypicalQuestionRobotKnowledgeBService typicalQuestionRobotKnowledgeBService;

    @Autowired
    private TypicalQuestionBService typicalQuestionBService;

    /**
     * 日志前缀
     */
    private static final String LOG_PREFIX = "[OnlineSessionSearchService]";
    /**
     * 索引前缀
     */
    private static final String SESSION_INDEX = "aida_session_online_";

    private static final Integer PAGE_NO = 1;

    private static final Integer PAGE_SIZE = 10;

    /**
     * 转人工发送类型
     */
    private static final String SENDER_STAFF = "STAFF";
    private static final String SENDER_VIRTUAL_KEFU = "VIRTUAL_KEFU";
    private static final String SENDER_WAIMAI_SHOP_BD = "WAIMAI_SHOP_BD";

    /**
     * 根据sessionId查询会话信息
     * @param sessionId
     * @return
     */
    public AidaOnlineSessionEsDTO findBySessionId(String sessionId) {
        SearchRequest searchRequest = new SearchRequest(EagleUtil.getIndices(null, null, SESSION_INDEX));
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        // sessionId
        if (StringUtils.isNotBlank(sessionId)) {
            boolQuery.must(QueryBuilders.termQuery(EsFieldConstants.SESSION_ID, sessionId));
        }

        sourceBuilder.query(boolQuery);
        searchRequest.source(sourceBuilder);
        // 2. 执行查询
        log.info("findBySessionId sourceBuilder = {}", sourceBuilder.toString());
        SearchHits searchHits = eagleService.search(searchRequest);
        log.info("findBySessionId searchHits = {}", JSON.toJSONString(searchHits));
        if (null == searchHits || searchHits.getHits().length == 0) {
            return null;
        }
        // 3. 解析结果
        SearchHit hit = searchHits.getHits()[0];
        return JSON.parseObject(hit.getSourceAsString(), AidaOnlineSessionEsDTO.class);
    }
    /**
     * 查询会话信息
     *
     * @param searchDTO 查询参数
     * @return 会话列表
     */
    public PageData<AidaOnlineSessionEsDTO> pageSessions(OnlineSessionSearchDTO searchDTO) {
        if (searchDTO == null) {
            log.error("{}-查询参数为空", LOG_PREFIX);
            return PageData.emptyData(PAGE_NO, PAGE_SIZE);
        }
        int pageNo = (searchDTO.getPageNo() == null || searchDTO.getPageNo() < 1) ? PAGE_NO : searchDTO.getPageNo();
        int pageSize = (searchDTO.getPageSize() == null || searchDTO.getPageSize() <= 0) ? PAGE_SIZE : searchDTO.getPageSize();
        try {
            // 1. 拼接查询条件
            SearchRequest searchRequest = new SearchRequest(EagleUtil.getIndices(searchDTO.getStartTime(), searchDTO.getEndTime(), SESSION_INDEX));
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            // sessionId
            if (CollectionUtils.isNotEmpty(searchDTO.getSessionIdList())) {
                boolQuery.must(QueryBuilders.termsQuery(EsFieldConstants.SESSION_ID, searchDTO.getSessionIdList()));
            }
            // 时间范围查询
            if (searchDTO.getStartTime() != null && searchDTO.getEndTime() != null) {
                RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery(EsFieldConstants.ADD_TIME);
                rangeQuery.gte(searchDTO.getStartTime());
                rangeQuery.lte(searchDTO.getEndTime());
                boolQuery.must(rangeQuery);
            }
            // 场景ID
            if (StringUtils.isNotBlank(searchDTO.getScene())) {
                boolQuery.must(QueryBuilders.termQuery(EsFieldConstants.BIZ_SCENE_ID, searchDTO.getScene()));
            }
            // 应用信息
            if (StringUtils.isNotBlank(searchDTO.getSpaceId()) || StringUtils.isNotBlank(searchDTO.getAppId()) || StringUtils.isNotBlank(searchDTO.getVersionId())) {
                BoolQueryBuilder appQueryBuilder = QueryBuilders.boolQuery();
                if (StringUtils.isNotBlank(searchDTO.getSpaceId())) {
                    appQueryBuilder.must(QueryBuilders.termQuery(EsFieldConstants.AIDA_APP_INFO + "." + EsFieldConstants.SPACE_ID, searchDTO.getSpaceId()));
                }
                if (StringUtils.isNotBlank(searchDTO.getAppId())) {
                    appQueryBuilder.must(QueryBuilders.termQuery(EsFieldConstants.AIDA_APP_INFO + "." + EsFieldConstants.APP_ID, searchDTO.getAppId()));
                }
                if (StringUtils.isNotBlank(searchDTO.getVersionId())) {
                    appQueryBuilder.must(QueryBuilders.termQuery(EsFieldConstants.AIDA_APP_INFO + "." + EsFieldConstants.VERSION_ID, searchDTO.getVersionId()));
                }
                boolQuery.must(QueryBuilders.nestedQuery(EsFieldConstants.AIDA_APP_INFO, appQueryBuilder, ScoreMode.None));
            }
            // 是否超时退出
            if (Boolean.TRUE.equals(searchDTO.getIsTimeoutEnd())) {
                boolQuery.must(QueryBuilders.termQuery(EsFieldConstants.IS_TIMEOUT_END, Boolean.TRUE.toString()));
            } else if (Boolean.FALSE.equals(searchDTO.getIsTimeoutEnd())) {
                boolQuery.mustNot(QueryBuilders.termQuery(EsFieldConstants.IS_TIMEOUT_END, Boolean.TRUE.toString()));
            }
            // 是否异常退出
            if (Boolean.TRUE.equals(searchDTO.getIsExceptionEnd())) {
                boolQuery.must(QueryBuilders.termQuery(EsFieldConstants.IS_EXCEPTION_END, Boolean.TRUE.toString()));
            } else if (Boolean.FALSE.equals(searchDTO.getIsExceptionEnd())) {
                boolQuery.mustNot(QueryBuilders.termQuery(EsFieldConstants.IS_EXCEPTION_END, Boolean.TRUE.toString()));
            }
            // 访问ID查询
            if (StringUtils.isNotBlank(searchDTO.getVisitId())) {
                boolQuery.must(QueryBuilders.termQuery(EsFieldConstants.VISIT_ID_LIST, searchDTO.getVisitId()));
            }
            // 标问查询
            if (StringUtils.isNotBlank(searchDTO.getQuestionName())) {
                BoolQueryBuilder orKnowledgeBoolQueryBuilder = QueryBuilders.boolQuery();
                // 查询标问ID
                log.info("{}-getQuestionId, question = {}", LOG_PREFIX, searchDTO.getQuestionName());
                List<TypicalQuestionRobotKnowledgeDTO> typicalQuestionRobotKnowledgeDTOList = typicalQuestionRobotKnowledgeBService.getByTypicalQuestionName(searchDTO.getQuestionName());
                log.info("{}-getByTypicalQuestionName, question = {}, res = {}", LOG_PREFIX, searchDTO.getQuestionName(), JSON.toJSONString(typicalQuestionRobotKnowledgeDTOList));
                TypicalQuestionQueryDTO typicalQuestionQueryDTO = new TypicalQuestionQueryDTO();
                typicalQuestionQueryDTO.setName(searchDTO.getQuestionName());
                QueryResponse<List<TypicalQuestionDTO>> listQueryResponse = typicalQuestionBService.pageAcrossLayer(typicalQuestionQueryDTO);
                log.info("{}-pageAcrossLayer, question = {}, res = {}", LOG_PREFIX, searchDTO.getQuestionName(), JSON.toJSONString(listQueryResponse));
                if (CollectionUtils.isEmpty(typicalQuestionRobotKnowledgeDTOList) && (null == listQueryResponse || CollectionUtils.isEmpty(listQueryResponse.getData()))) {
                    return PageData.emptyData(pageNo, pageSize);
                }
                if (CollectionUtils.isNotEmpty(typicalQuestionRobotKnowledgeDTOList)) {
                    for (TypicalQuestionRobotKnowledgeDTO dto : typicalQuestionRobotKnowledgeDTOList) {
                        BoolQueryBuilder andKnowledgeBoolQueryBuilder = QueryBuilders.boolQuery().must(QueryBuilders.termQuery(EsFieldConstants.KNOWLEDGE_ID_LIST, dto.getRobotKnowledgeId()));
                        orKnowledgeBoolQueryBuilder.should(andKnowledgeBoolQueryBuilder);
                    }
                }
                if (CollectionUtils.isNotEmpty(listQueryResponse.getData())) {
                    for (TypicalQuestionDTO typicalQuestionDTO : listQueryResponse.getData()) {
                        BoolQueryBuilder andKnowledgeBoolQueryBuilder = QueryBuilders.boolQuery().must(QueryBuilders.termQuery(EsFieldConstants.KNOWLEDGE_ID_LIST, typicalQuestionDTO.getId()));
                        orKnowledgeBoolQueryBuilder.should(andKnowledgeBoolQueryBuilder);
                    }
                }
                boolQuery.must(orKnowledgeBoolQueryBuilder);
            }
            // 是否解决
            if (StringUtils.isNotBlank(searchDTO.getSessionSolved())) {
                if (QuestionSolveEnum.NO_EVALUATE.getCode().equals(searchDTO.getSessionSolved())) {
                    boolQuery.mustNot(QueryBuilders.existsQuery(EsFieldConstants.SESSION_SOLVED));
                } else {
                    boolQuery.must(QueryBuilders.termQuery(EsFieldConstants.SESSION_SOLVED, searchDTO.getSessionSolved()));
                }
            }
            // 评价星级
            if (CollectionUtils.isNotEmpty(searchDTO.getStars())) {
                BoolQueryBuilder orStar = QueryBuilders.boolQuery();
                for (String star : searchDTO.getStars()) {
                    BoolQueryBuilder andStar = QueryBuilders.boolQuery();
                    if (EvaluationStarEnum.NO_STARS.getCode().equals(star)) {
                        andStar.mustNot(QueryBuilders.existsQuery(EsFieldConstants.STARS));
                    } else {
                        andStar.must(QueryBuilders.termQuery(EsFieldConstants.STARS, star));
                    }
                    orStar.should(andStar);
                }
                boolQuery.must(orStar);
            }
            // 业务
            if (StringUtils.isNotBlank(searchDTO.getBu())) {
                boolQuery.must(QueryBuilders.termQuery(EsFieldConstants.BU, searchDTO.getBu()));
            }
            // 子业务
            if (StringUtils.isNotBlank(searchDTO.getSubBu())) {
                boolQuery.must(QueryBuilders.termQuery(EsFieldConstants.SUB_BU, searchDTO.getSubBu()));
            }
            // 用户ID
            if (StringUtils.isNotBlank(searchDTO.getUserId())) {
                boolQuery.must(QueryBuilders.termQuery(EsFieldConstants.USER_ID, searchDTO.getUserId()));
            }
            // 订单ID
            if (StringUtils.isNotBlank(searchDTO.getOrderId())) {
                boolQuery.must(QueryBuilders.termQuery(EsFieldConstants.ORDER_ID_LIST, searchDTO.getOrderId()));
            }
            // 转人工
            buildTransferStaffQuery(boolQuery, searchDTO.getTransferStaff());
            // task
            if (StringUtils.isNotBlank(searchDTO.getTaskKey()) || StringUtils.isNotBlank(searchDTO.getTaskVersion()) || StringUtils.isNotBlank(searchDTO.getTaskNodeId())) {
                BoolQueryBuilder appQueryBuilder = QueryBuilders.boolQuery();
                if (StringUtils.isNotBlank(searchDTO.getTaskKey())) {
                    appQueryBuilder.must(QueryBuilders.termQuery(EsFieldConstants.TASK_TRACE_LOGS + "." + EsFieldConstants.TASK_KEY, searchDTO.getTaskKey()));
                }
                if (StringUtils.isNotBlank(searchDTO.getTaskVersion())) {
                    appQueryBuilder.must(QueryBuilders.termQuery(EsFieldConstants.TASK_TRACE_LOGS + "." + EsFieldConstants.TASK_VERSION, searchDTO.getTaskVersion()));
                }
                if (StringUtils.isNotBlank(searchDTO.getTaskNodeId())) {
                    appQueryBuilder.must(QueryBuilders.termQuery(EsFieldConstants.TASK_TRACE_LOGS + "." + EsFieldConstants.TASK_NODE_IDS, searchDTO.getTaskNodeId()));
                }
                boolQuery.must(QueryBuilders.nestedQuery(EsFieldConstants.TASK_TRACE_LOGS, appQueryBuilder, ScoreMode.None));
            }
            // 消息
            if (StringUtils.isNotBlank(searchDTO.getQueryContent())) {
                BoolQueryBuilder messageQueryBuilder = QueryBuilders.boolQuery();
                if (StringUtils.isNotBlank(searchDTO.getQueryContent())) {
                    messageQueryBuilder.must(QueryBuilders.matchQuery(EsFieldConstants.MESSAGES + "." + EsFieldConstants.MESSAGE, searchDTO.getQueryContent()));
                    messageQueryBuilder.must(QueryBuilders.termQuery(EsFieldConstants.MESSAGES + "." + EsFieldConstants.ROLE, DialogRoleEnum.CUSTOMER.getName()));
                }
                boolQuery.must(messageQueryBuilder);
            }
            if (StringUtils.isNotBlank(searchDTO.getResponseContent())) {
                BoolQueryBuilder messageQueryBuilder = QueryBuilders.boolQuery();
                if (StringUtils.isNotBlank(searchDTO.getResponseContent())) {
                    messageQueryBuilder.must(QueryBuilders.matchQuery(EsFieldConstants.MESSAGES + "." + EsFieldConstants.MESSAGE, searchDTO.getResponseContent()));
                    messageQueryBuilder.must(QueryBuilders.termsQuery(EsFieldConstants.MESSAGES + "." + EsFieldConstants.ROLE, DialogRoleEnum.STAFF.getName(), DialogRoleEnum.SMART_LLM.getName(), DialogRoleEnum.VIRTUAL_LLM.getName()));
                }
                boolQuery.must(messageQueryBuilder);
            }
            sourceBuilder.sort(new FieldSortBuilder(EsFieldConstants.ADD_TIME).order(SortOrder.DESC));
            sourceBuilder.size(pageSize);
            sourceBuilder.from((pageNo - 1) * pageSize);
            sourceBuilder.timeout(new TimeValue(5, TimeUnit.SECONDS));
            sourceBuilder.query(boolQuery);
            sourceBuilder.trackTotalHits(true);
            sourceBuilder.fetchSource(null, new String[]{EsFieldConstants.TASK_TRACE_LOGS, EsFieldConstants.MESSAGES});
            searchRequest.source(sourceBuilder);
            // 2. 执行查询
            log.info("pageSessions sourceBuilder = {}", sourceBuilder.toString());
            SearchHits searchHits = eagleService.search(searchRequest);
            log.info("pageSessions searchHits = {}", JSON.toJSONString(searchHits));
            if (null == searchHits || searchHits.getHits().length == 0) {
                return PageData.emptyData(pageNo, pageSize);
            }
            // 3. 解析结果
            List<AidaOnlineSessionEsDTO> resultList = Lists.newArrayList();
            for (SearchHit hit : searchHits.getHits()) {
                AidaOnlineSessionEsDTO sessionDTO = JSON.parseObject(hit.getSourceAsString(), AidaOnlineSessionEsDTO.class);
                resultList.add(sessionDTO);
            }
            return PageData.create(searchHits.getTotalHits().value, pageNo, pageSize, resultList);
        } catch (Exception e) {
            log.error("{}-searchSessions error, params: {}", LOG_PREFIX, JSON.toJSONString(searchDTO), e);
        }
        return PageData.emptyData(pageNo, pageSize);
    }

    /**
     * 转人工查询条件构建
     *
     * @param queryBuilder      查询条件
     * @param transferStaffList 转人工条件
     */
    private void buildTransferStaffQuery(BoolQueryBuilder queryBuilder, List<String> transferStaffList) {
        // 转人工搜索条件
        if (CollectionUtils.isNotEmpty(transferStaffList)) {
            BoolQueryBuilder andChatNoChatRequest = QueryBuilders.boolQuery();
            BoolQueryBuilder andChatNoChatBegin = QueryBuilders.boolQuery();
            BoolQueryBuilder andChatBegin = QueryBuilders.boolQuery();
            BoolQueryBuilder andTransferBD = QueryBuilders.boolQuery();
            BoolQueryBuilder orChat = QueryBuilders.boolQuery();
            // 未申请转人工
            if (transferStaffList.contains(TransferStaffEnum.NO_CHAT_REQUEST.getCode())) {
                BoolQueryBuilder typeList = QueryBuilders.boolQuery();
                typeList.mustNot(QueryBuilders.termQuery(EsFieldConstants.TYPE_LIST, ChatRequestEnum.CHAT_REQUEST_YES.getCode()));
                typeList.mustNot(QueryBuilders.termQuery(EsFieldConstants.TYPE_LIST, ChatRequestEnum.CHAT_BEGIN_YES.getCode()));
                BoolQueryBuilder senderTypeList = QueryBuilders.boolQuery();
                senderTypeList.must(QueryBuilders.termQuery(EsFieldConstants.SENDER_TYPE_LIST, SENDER_VIRTUAL_KEFU));
                senderTypeList.mustNot(QueryBuilders.termQuery(EsFieldConstants.SENDER_TYPE_LIST, SENDER_STAFF));
                andChatNoChatRequest.should(typeList);
                andChatNoChatRequest.should(senderTypeList);
                orChat.should(andChatNoChatRequest);
            }
            // 在线转人工（成功接入）
            if (transferStaffList.contains(TransferStaffEnum.NO_CHAT_BEGIN.getCode())) {
                andChatNoChatBegin.must(QueryBuilders.termQuery(EsFieldConstants.TYPE_LIST, ChatRequestEnum.CHAT_REQUEST_YES.getCode()));
                andChatNoChatBegin.mustNot(QueryBuilders.termQuery(EsFieldConstants.TYPE_LIST, ChatRequestEnum.CHAT_BEGIN_YES.getCode()));
                andChatNoChatBegin.mustNot(QueryBuilders.termQuery(EsFieldConstants.SENDER_TYPE_LIST, SENDER_VIRTUAL_KEFU));
                orChat.should(andChatNoChatBegin);
            }
            // 在线转人工（未接入）
            if (transferStaffList.contains(TransferStaffEnum.CHAT_BEGIN.getCode())) {
                BoolQueryBuilder builder1 = QueryBuilders.boolQuery();
                builder1.must(QueryBuilders.termQuery(EsFieldConstants.TYPE_LIST, ChatRequestEnum.CHAT_REQUEST_YES.getCode()));
                builder1.must(QueryBuilders.termQuery(EsFieldConstants.TYPE_LIST, ChatRequestEnum.CHAT_BEGIN_YES.getCode()));
                builder1.mustNot(QueryBuilders.termQuery(EsFieldConstants.SENDER_TYPE_LIST, SENDER_VIRTUAL_KEFU));
                andChatBegin.should(builder1);
                BoolQueryBuilder builder2 = QueryBuilders.boolQuery();
                builder2.must(QueryBuilders.termQuery(EsFieldConstants.SENDER_TYPE_LIST, SENDER_VIRTUAL_KEFU));
                builder2.must(QueryBuilders.termQuery(EsFieldConstants.SENDER_TYPE_LIST, SENDER_STAFF));
                andChatBegin.should(builder2);
                orChat.should(andChatBegin);
            }
            // 转BD
            if (transferStaffList.contains(TransferStaffEnum.TRANSFER_BD.getCode())) {
                andTransferBD.must(QueryBuilders.termQuery(EsFieldConstants.SENDER_TYPE_LIST, SENDER_WAIMAI_SHOP_BD));
                orChat.should(andTransferBD);
            }
            queryBuilder.must(orChat);
        }
    }

}
