package com.meituan.csc.aigc.eval.service.analysis.dialog;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.csc.portal.message.dto.MessageDTO;
import com.dianping.csc.portal.message.dto.QueryDTO;
import com.dianping.csc.portal.message.dto.ResponseDTO;
import com.dianping.csc.portal.message.service.MessageFacadeService;
import com.dianping.csc.portal.open.enums.SenderTypeEnum;
import com.google.common.collect.Lists;
import com.meituan.csc.aigc.eval.constants.CatConstants;
import com.meituan.csc.aigc.eval.dto.es.AidaOnlineSessionEsDTO;
import com.meituan.csc.aigc.eval.enums.es.DialogRoleEnum;
import com.meituan.csc.aigc.eval.enums.es.MessageTypeEnum;
import com.meituan.csc.aigc.eval.enums.es.PortalMessageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 门户消息类型处理服务(处理逻辑同Agent)
 *
 * <AUTHOR>
 * @date 2025/5/25
 */
@Slf4j
@Service
public class PortalDialogHelper {

    @Autowired
    private MessageFacadeService messageFacadeService;

    /**
     * 消息前缀
     */
    private static final String LOG_PREFIX = "[PortalDialogHelper]";

    /**
     * 过滤消息类型
     */
    public static final List<MessageTypeEnum> FILTER_MESSAGE_TYPE = Lists.newArrayList(
            MessageTypeEnum.COUPON_LIST_COMPONENT,
            MessageTypeEnum.COUPON_LIST_CONFIRM,
            MessageTypeEnum.CHAT_CALL_OUT,
            MessageTypeEnum.No_ORDER_LIST_COMPONENT,
            MessageTypeEnum.NO_ORDER_LIST_COMPONENT_END,
            MessageTypeEnum.UPDATE_MESSAGE,
            MessageTypeEnum.ORDER_LIST,
            MessageTypeEnum.ORDER_SELECTOR,
            MessageTypeEnum.ORDER_SELECTOR_END
    );

    /**
     * 选项消息列表
     */
    private static final List<String> OPTION_LIST = Lists.newArrayList(
            "是否为当前订单",
            "亲亲，请问您现在是希望【继续入住】还是【申请退款】呀?",
            "亲亲，再跟您确认一下您的诉求呀，您现在是希望【继续入住】还是【申请退款】呀?",
            "辛苦您点击确认一下，小美才能帮您处理哦~",
            "亲亲，请您确认一下你大概想要几点办理入住呀？",
            "亲亲，请您确认一下你大概想要延迟到几点办理退房呀？",
            "亲亲，跟您确认一下现在是希望【继续入住】还是【申请退款】呀?",
            "亲亲，跟您再确认一下现在是希望【继续入住】还是【申请退款】呀?"
    );

    /**
     * 获取处理后的门户消息
     *
     * @param sessionId 会话ID
     * @return 消息列表
     */
    public List<AidaOnlineSessionEsDTO.DialogMessage> listHandleMessage(String sessionId) {
        // 查询门户消息
        List<MessageDTO> portalMessageList = listHistoryMessage(sessionId);
        if (CollectionUtils.isEmpty(portalMessageList)) {
            log.error("{}-listHistoryMessage empty, sessionId = {}", LOG_PREFIX, sessionId);
            return Lists.newArrayList();
        }
        // 消息转换
        List<AidaOnlineSessionEsDTO.DialogMessage> dialogMessageList = portalMessageList.stream()
                .filter(message -> !isSpecialType(message))
                .map(this::convertToEsMessage)
                .collect(Collectors.toList());
        // 格式处理
        return handleMessageList(dialogMessageList);
    }


    /**
     * 获取历史上文
     *
     * @param sessionId 会话ID
     * @return 消息列表
     */
    private List<MessageDTO> listHistoryMessage(String sessionId) {
        Transaction transaction = Cat.newTransaction(CatConstants.ANALYSIS_ES_RPC_TYPE, CatConstants.PORTAL_MESSAGE);
        List<MessageDTO> messageDTOList = Lists.newArrayList();
        try {
            QueryDTO queryDTO = new QueryDTO();
            queryDTO.setSessionId(Long.parseLong(sessionId));
            queryDTO.setSize(80);
            queryDTO.setMessageTypeList(Arrays.stream(PortalMessageTypeEnum.values()).map(PortalMessageTypeEnum::getValue).collect(Collectors.toList()));

            ResponseDTO<List<MessageDTO>> responseDTO = messageFacadeService.getHistoryListBySessionIdDescByRhino(queryDTO);
            if (Objects.isNull(responseDTO) || !responseDTO.isSuccess() || Objects.isNull(responseDTO.getData())) {
                log.error("{}-getHistoryListBySessionIdDesc error, responseDTO = {}", LOG_PREFIX, JSON.toJSONString(responseDTO));
                transaction.setStatus("-1");
                return messageDTOList;
            }
            return responseDTO.getData();
        } catch (Exception e) {
            log.error("{}-listHistoryMessage error, sessionId = {}, e:", LOG_PREFIX, sessionId, e);
            transaction.setStatus(e);
        } finally {
            transaction.complete();
        }
        return messageDTOList;
    }

    /**
     * 处理消息列表
     *
     * @param dialogMessageList 门户消息列表
     * @return 处理后的消息列表
     */
    private List<AidaOnlineSessionEsDTO.DialogMessage> handleMessageList(List<AidaOnlineSessionEsDTO.DialogMessage> dialogMessageList) {
        // 将历史对话信息按照转人工前后进行分类
        Map<String, List<AidaOnlineSessionEsDTO.DialogMessage>> messageMap = dialogMessageList.stream()
                .collect(Collectors.groupingBy(AidaOnlineSessionEsDTO.DialogMessage::getTransferStaff));
        // 处理转人工前的历史对话信息
        List<String> roleList = Lists.newArrayList(DialogRoleEnum.CUSTOMER.getName(), DialogRoleEnum.SMART_LLM.getName());
        List<AidaOnlineSessionEsDTO.DialogMessage> beforeMessages = handleMessageContent(messageMap.get(Boolean.FALSE.toString()), roleList);
        // 处理转人工后的历史对话信息
        roleList = Lists.newArrayList(DialogRoleEnum.CUSTOMER.getName(), DialogRoleEnum.STAFF.getName(), DialogRoleEnum.VIRTUAL_LLM.getName());
        List<AidaOnlineSessionEsDTO.DialogMessage> afterMessages = handleMessageContent(messageMap.get(Boolean.TRUE.toString()), roleList);
        return Stream.concat(Optional.ofNullable(beforeMessages).orElse(Collections.emptyList()).stream(), Optional.ofNullable(afterMessages).orElse(Collections.emptyList()).stream())
                .sorted(Comparator.comparing(AidaOnlineSessionEsDTO.DialogMessage::getTime).thenComparing(AidaOnlineSessionEsDTO.DialogMessage::getMessageId))
                .collect(Collectors.toList());
    }

    /**
     * 处理消息内容
     *
     * @param dialogMessageList 消息列表
     * @param roleList          要保留的角色
     * @return 处理消息内容后的消息列表
     */
    private List<AidaOnlineSessionEsDTO.DialogMessage> handleMessageContent(List<AidaOnlineSessionEsDTO.DialogMessage> dialogMessageList, List<String> roleList) {
        if (CollectionUtils.isEmpty(dialogMessageList)) {
            return Lists.newArrayList();
        }
        List<AidaOnlineSessionEsDTO.DialogMessage> afterTransMessagesList = dialogMessageList.stream()
                .filter(message -> roleList.contains(message.getRole()))
                .filter(message -> MessageTypeEnum.findByValue(message.getType()) == null || !FILTER_MESSAGE_TYPE.contains(MessageTypeEnum.findByValue(message.getType())))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(afterTransMessagesList)) {
            return Lists.newArrayList();
        }
        return afterTransMessagesList.stream()
                // 预处理
                .peek(dialog -> dialog.setMessage(portalTextProcess(dialog)))
                .filter(item -> StringUtils.isNotBlank(item.getMessage()))
                .collect(Collectors.toList());
    }

    /**
     * 消息格式转换
     * 
     * @param messageDTO 门户消息
     * @return ES消息
     */
    private AidaOnlineSessionEsDTO.DialogMessage convertToEsMessage(MessageDTO messageDTO) {
        AidaOnlineSessionEsDTO.DialogMessage dialogMessage = new AidaOnlineSessionEsDTO.DialogMessage();
        dialogMessage.setMessageId(messageDTO.getId());
        dialogMessage.setMessage(messageDTO.getData());
        dialogMessage.setType(messageDTO.getType());
        dialogMessage.setSenderType(messageDTO.getSenderType());
        dialogMessage.setRole(getRole(messageDTO));
        dialogMessage.setTime(messageDTO.getAddTime());
        dialogMessage.setTransferStaff(String.valueOf(Objects.nonNull(messageDTO.getStaffId())));
        return dialogMessage;
    }

    /**
     * 获取消息角色
     * 
     * @param messageDTO 门户消息
     * @return 角色
     */
    private String getRole(MessageDTO messageDTO) {
        if (SenderTypeEnum.CUSTOMER.name().equals(messageDTO.getSenderType())) {
            return DialogRoleEnum.CUSTOMER.getName();
        } else if (SenderTypeEnum.STAFF.name().equals(messageDTO.getSenderType())) {
            return DialogRoleEnum.STAFF.getName();
        } else if (SenderTypeEnum.SYSTEM.name().equals(messageDTO.getSenderType())) {
            return DialogRoleEnum.SYSTEM.getName();
        } else if (SenderTypeEnum.TASK.name().equals(messageDTO.getSenderType())
                && PortalMessageTypeEnum.FAQ_ANSWER.getValue().equals(messageDTO.getType())
                && isLlmReply(messageDTO.getData())) {
            return DialogRoleEnum.SMART_LLM.getName();
        } else if (SenderTypeEnum.STAFF_ROBOT.name().equals(messageDTO.getSenderType())) { // 托管消息
            return DialogRoleEnum.STAFF.getName();
        } else if (SenderTypeEnum.VIRTUAL_KEFU.name().equals(messageDTO.getSenderType())) { // 虚拟客服消息
            return DialogRoleEnum.VIRTUAL_LLM.getName();
        } else {
            return DialogRoleEnum.OTHER.getName();
        }
    }

    /**
     * 是否是智能大模型消息
     * 
     * @param faqAnswer 消息内容
     * @return 是否是智能大模型消息
     */
    private boolean isLlmReply(String faqAnswer) {
        if (StringUtils.isEmpty(faqAnswer)) {
            return false;
        }
        try {
            Map<String, Object> map = JSON.parseObject(faqAnswer, new TypeReference<Map<String, Object>>() {});
            if (MapUtils.isEmpty(map)) {
                return false;
            }
            Map<String, Object> extendInfoMap = JSON.parseObject(JSON.toJSONString(map.getOrDefault("extendInfo", "{}")), new TypeReference<Map<String, Object>>() {
            });
            if (MapUtils.isEmpty(extendInfoMap)) {
                return false;
            }
            // 大模型回复字段
            String llm = String.valueOf(extendInfoMap.get("llm"));
            return Objects.equals(llm, "1");
        } catch (Exception e) {
            log.error("{}-isLlmReply error, faqAnswer = {}, e:", LOG_PREFIX, faqAnswer, e);
            return false;
        }
    }

    /**
     * 是否是特殊类型消息
     * 
     * @param message 门户消息
     * @return 是否是特殊类型消息
     */
    private boolean isSpecialType(MessageDTO message) {
        return SenderTypeEnum.CUSTOMER.name().equals(message.getSenderType())
                && PortalMessageTypeEnum.TEXT.getValue().equals(message.getType())
                && Objects.equals(JSONObject.parseObject(message.getData()).getInteger("type"), 8);
    }

    /************** 消息格式处理 ***************/
    /**
     * 处理文门消息
     *
     * @param dialogMessage 消息
     * @return 处理后的消息内容
     */
    private static String portalTextProcess(AidaOnlineSessionEsDTO.DialogMessage dialogMessage) {
        try {
            PortalMessageTypeEnum type = PortalMessageTypeEnum.findByValue(dialogMessage.getType());
            if (null == type) {
                return StringUtils.EMPTY;
            }
            String message = dialogMessage.getMessage();
            switch (type) {
                case TEXT:
                    return getText(message);
                case FAQ:
                    return faqCategoryMessageHandler(message);
                case VOICE:
                    return Optional.ofNullable(message).orElse("");
                case CHAT_REQUEST:
                    return Optional.ofNullable(JSONObject.parseObject(message).getString("consultDescription")).orElse("");
                case IMAGE:
                    return "【图片】";
                case FILE:
                    return "[文件]";
                case FAQ_ANSWER:
                    String handledMessage = faqAnswerHandler(message);
                    return repHandler(handledMessage);
                case OPTION:
                    return handleOptionMessage(message);
                case ORDER_CONSULT:
                    return getContent(message);
                case ORDER:
                    return "[订单]";
                case ORDER_CONFIRM:
                    // 只需要客服发送的确认订单
                    if (!Objects.equals(dialogMessage.getRole(), DialogRoleEnum.STAFF.getName())) {
                        return "";
                    }
                    return "[确认订单]";
                case NO_ORDER_LIST_CONFIRM:
                    return getNoOrderListConfirmText(message);
                case LIST_ITEM_CONFIRM_QUERY:
                    // 获取用户点选的通用列表组件选项的Content
                    return getText(message);
                default:
                    return StringUtils.EMPTY;
            }
        } catch (Exception e) {
            log.error("{}-portalTextProcess error, e:", LOG_PREFIX, e);
            return StringUtils.EMPTY;
        }
    }


    /**
     * faqCategory类型消息处理
     *
     * @param beforeMessage 消息
     * @return beforeMessage中文本内容 - title
     */
    private static String faqCategoryMessageHandler(String beforeMessage) {
        if (StringUtils.isBlank(beforeMessage)) {
            return StringUtils.EMPTY;
        }
        try {
            JSONObject beforeMessageJson = Optional.ofNullable(JSON.parseObject(beforeMessage)).orElse(new JSONObject());
            String title = beforeMessageJson.getString("title");
            if (StringUtils.isNotBlank(title)) {
                return title;
            }
        } catch (Exception e) {
            log.error("{}-faqCategoryMessageHandler error, message = {}, e:", LOG_PREFIX, beforeMessage, e);
        }
        return beforeMessage;
    }

    /**
     * 获取消息内容文本
     *
     * @param beforeMessage 处理前消息
     * @return 处理后消息
     */
    private static String getText(String beforeMessage) {
        try {
            JSONObject jsonObject = JSONObject.parseObject(beforeMessage);
            String content = jsonObject.getString("content");
            Integer type = jsonObject.getInteger("type");
            if (Objects.nonNull(type)) {
                // 对订单列表点选结果做特殊处理
                Integer ORDER_LIST_TYPE = 11;
                if (ORDER_LIST_TYPE.equals(type) && StringUtils.isNotBlank(jsonObject.getString("orderId"))) {
                    content = "嗯，是这个订单";
                }
            }
            return content;
        } catch (Exception e) {
            log.error("{} getText, message = {}, e:", LOG_PREFIX, beforeMessage, e);
            return beforeMessage;
        }
    }

    /**
     * 解析门户消息，获取json中内容
     *
     * @param beforeMessage 处理前消息
     * @return 处理后消息内容
     */
    private static String getContent(String beforeMessage) {
        try {
            return JSONObject.parseObject(beforeMessage).getString("content");
        } catch (Exception e) {
            return beforeMessage;
        }
    }

    /**
     * 获取订单列表确认文本
     *
     * @param beforeMessage 处理前消息
     * @return 订单列表确认文本
     */
    private static String getNoOrderListConfirmText(String beforeMessage) {
        try {
            JSONObject jsonObject = JSONObject.parseObject(beforeMessage);
            String confirmTextJsonKey = "confirmText";
            return jsonObject.getString(confirmTextJsonKey);
        } catch (Exception e) {
            return beforeMessage;
        }
    }

    /**
     * 处理选项消息
     *
     * @param beforeMessage 处理前消息
     * @return 选项内容
     */
    private static String handleOptionMessage(String beforeMessage) {
        String content = getText(beforeMessage);
        return messageExactMatch(content);
    }

    /**
     * 精确匹配
     *
     * @param content 选项内容
     * @return 匹配结果
     */
    private static String messageExactMatch(String content) {
        if (PortalDialogHelper.OPTION_LIST.contains(content)) {
            return content;
        }
        // 没匹配到的消息格式, 丢弃
        return StringUtils.EMPTY;
    }

    /**
     * 针对text类型文本，定制化进行正则处理
     *
     * @param message 消息
     * @return 处理结果
     */
    private static String repHandler(String message) {
        if (StringUtils.isEmpty(message)) {
            return StringUtils.EMPTY;
        }
        message = extractQuoteReply(message);
        message = replaceImg(message, "【图片】");
        message = replaceTime(message, "【时刻】");
        message = replaceBlank(message, " ");
        message = extractQuoteReply(message);
        message = removeDiv(message, "");
        message = removeKefuLastReplyPattern(message, "");
        message = removeHtmlTags(message, "");
        message = removeEmo(message, "");
        message = removeDupBlank(message, " ");
        message = replaceAndRemoveTags(message);
        message = replaceHostVerifyPattern(message, "【提示】:稍等一下哈，验证host中。");
        message = replaceChatIdentityVerifyPattern(message, "【提示】:请完成身份验证");
        message = replaceNoPassPattern(message, "【提示】:该用户核身不通过");
        message = replacePassPattern(message, "【提示】:核身通过");
        message = replaceContentPattern(message, "");
        message = replaceContentPattern2(message, "");
        return message;
    }


    // 将图片链接替换为相应文本
    public static String replaceImg(String text, String targetStr) {
        return text.replaceAll("<img src=\".+\" class=\"upload-img\" />|<img.*?>", targetStr);
    }

    public static String replaceHostVerifyPattern(String text, String targetStr) {
        return text.replaceAll("\\{\\\"channelVOs.*isDynamicAnswer\\\":false\\}", targetStr);
    }

    public static String replaceChatIdentityVerifyPattern(String text, String targetStr) {
        return text.replaceAll("\\{\"status.*userType\":1\\}", targetStr);
    }

    public static String replaceNoPassPattern(String text, String targetStr) {
        return text.replaceAll("\\{\\\"verifyStatus\\\":1.*核身不通过.*openId\\\":\\\"\\d{1,}\\\"\\}", targetStr);
    }

    public static String replacePassPattern(String text, String targetStr) {
        return text.replaceAll("\\{\\\"verifyStatus\\\":0.*核身通过.*openId\\\":\\\"\\d{1,}\\\"\\}", targetStr);
    }

    static String replaceContentPattern(String text, String targetStr) {
        return text.replaceAll("\\{\\\"content\\\".*businessSceneId\\\":\\\"\\d{1,}\\\"\\}\\}", targetStr);
    }

    public static String replaceContentPattern2(String text, String targetStr) {
        return text.replaceAll("\\{\\\"content\\\".*extMap\\\":null\\}", targetStr);
    }

    // 替换空白
    public static String replaceBlank(String text, String targetStr) {
        return text.replaceAll("&nbsp;|\\t|\\n|\\u2006", targetStr);
    }

    // 抽取引用答案，暂时直接删除了引用的原文
    public static String extractQuoteReply(String text) {
        Pattern compiledPattern = Pattern.compile("<div class=\"quote-last-reply\">(.*?)</div>");
        Matcher matcher = compiledPattern.matcher(text);
        if (matcher.find()) {
            return matcher.group(1);
        } else {
            return text;
        }
    }

    // 去除<div>等特殊符号
    public static String removeDiv(String text, String targetStr) {
        return text.replaceAll("<div>|</div>|<div class=\"quote-last-reply\">|<span class=\"text-secret\">|</span>", targetStr);
    }

    public static String removeKefuLastReplyPattern(String text, String targetStr) {
        return text.replaceAll("\\n客服:<div class=\"quote-prev-reply\"><br>", targetStr);
    }

    //去除/p及/span
    public static String removeHtmlTags(String text, String replacement) {
        return text.replaceAll("<(/)?(p|span).*?>", replacement);
    }


    // 将转码后的空格替换回去
    public static String removeDupBlank(String text, String targetStr) {
        return text.replaceAll("\\s{2,}", targetStr);
    }

    // 将一长段时间替换为“某时刻”
    public static String replaceTime(String text, String targetStr) {
        return text.replaceAll("\\d{4}-\\d{1,2}-\\d{1,2} \\d{1,2}:\\d{1,2}:\\d{1,2}", targetStr);
    }

    // 移除表情符号
    public static String removeEmo(String text, String targetStr) {
        return text.replaceAll("&#\\d+;", targetStr);
    }

    // 针对faqAnswer类型文本，定制化进行内容提取
    public static String faqAnswerHandler(String message) {
        if (org.apache.commons.lang.StringUtils.isEmpty(message)) {
            return "";
        }
        Map<String, Object> map = JSON.parseObject(message, new TypeReference<Map<String, Object>>() {
        });
        if (null == map || map.isEmpty()) {
            return "";
        }
        return (String) map.getOrDefault("answer", "");
    }

    // 处理html的特殊符号
    public static String replaceAndRemoveTags(String text) {
        // 去除<br> 为 空格
        text = text.replace("<br>", " ");
        // 移除div、超链接等
        text = text.replaceAll("<.*?>", "");
        // HTML直接转义
        text = text.replace("&gt;", ">")
                .replace("&lt;", "<")
                .replace("&amp;", "&")
                .replace("&quot;", "\"");
        return text;
    }

}
