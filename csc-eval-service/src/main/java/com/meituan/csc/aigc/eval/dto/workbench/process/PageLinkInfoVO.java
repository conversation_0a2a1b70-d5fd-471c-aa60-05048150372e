package com.meituan.csc.aigc.eval.dto.workbench.process;

import lombok.Data;

import java.io.Serializable;

/**
 * 页面跳转链路基础数据
 */
@Data
public class PageLinkInfoVO implements Serializable {
    /**
     * 业务类型ID
     */
    protected Integer businessTypeId;
    /**
     * 子业务类型ID
     */
    protected Integer subBusinessTypeId;

    /**
     * 业务类型编码
     */
    protected String businessTypeCode;
    /**
     * 子业务类型编码
     */
    protected String subBusinessTypeCode;

    /**
     * 业务类型名称
     */
    protected String businessTypeName;
    /**
     * 子业务类型名称
     */
    protected String subBusinessTypeName;

    /**
     * 机器人ID
     */
    protected Integer robotId;

    /**
     * 机器人名称
     */
    protected String robotName;

    /**
     * 启用路径
     */
    protected String enablePath;
    /**
     * 机器人类型ID
     */
    protected Integer robotTypeId;
    /**
     * 机器人类型名称
     */
    protected String robotTypeName;

    /**
     * 是否是业务机器人
     *
     * @return
     */
    public Boolean isServiceRobot() {
        return robotTypeId != null && robotTypeId == 0;
    }
}
