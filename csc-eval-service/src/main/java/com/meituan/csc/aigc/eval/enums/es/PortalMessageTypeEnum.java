package com.meituan.csc.aigc.eval.enums.es;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
@AllArgsConstructor
public enum PortalMessageTypeEnum {
    TEXT("Text"),
    FAQ("FaqCategory"),
    VOICE("voice"),
    CHAT_REQUEST("ChatRequest"),
    IMAGE("Image"),
    FILE("File"),
    FAQ_ANSWER("FaqAnswer"),
    OPTION("option"),
    ORDER_CONSULT("OrderConsult"),
    ORDER("Order"),
    ORDER_CONFIRM("OrderConfirm"),
    NO_ORDER_LIST_CONFIRM("NoOrderListConfirm"),
    LIST_ITEM_CONFIRM_QUERY("ListItemConfirmQuery");

    private final String value;

    public static PortalMessageTypeEnum findByValue(String value) {
        return Arrays.stream(PortalMessageTypeEnum.values()).filter(e -> Objects.equals(e.getValue(), value)).findFirst().orElse(null);
    }

}