package com.meituan.csc.aigc.eval.mq;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.csc.eagle.query.dto.portal.RobotInvokeLog;
import com.dianping.csc.eagle.query.dto.portal.RobotInvokeLogSearchResult;
import com.dianping.csc.eagle.query.dto.portal.SessionSearchResult;
import com.dianping.csc.eagle.query.dto.portal.TaskLogSearchResult;
import com.google.common.collect.Lists;
import com.meituan.csc.aigc.eval.constants.CatConstants;
import com.meituan.csc.aigc.eval.dto.es.AidaOnlineSessionCacheDTO;
import com.meituan.csc.aigc.eval.dto.es.AidaOnlineSessionEsDTO;
import com.meituan.csc.aigc.eval.dto.mq.producer.AidaAnalysisSessionDTO;
import com.meituan.csc.aigc.eval.enums.es.mq.AnalysisMessageTypeEnum;
import com.meituan.csc.aigc.eval.enums.es.online.QuestionSolveEnum;
import com.meituan.csc.aigc.eval.proxy.PortalEagleServiceProxy;
import com.meituan.csc.aigc.eval.service.analysis.OnlineSessionRedisService;
import com.meituan.csc.aigc.eval.service.analysis.dialog.PortalDialogHelper;
import com.meituan.csc.aigc.eval.service.analysis.es.CaseAnalysisEagleService;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mdp.boot.starter.mafka.consumer.anno.MdpMafkaMsgReceive;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.get.GetRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/5/22
 */
@Slf4j
@Service("aidaAnalysisSessionListener")
public class AidaAnalysisSessionListener {

    @Autowired
    private CaseAnalysisEagleService eagleService;

    @Autowired
    private OnlineSessionRedisService redisService;

    @Autowired
    private PortalEagleServiceProxy portalEagleServiceProxy;

    @Autowired
    private PortalDialogHelper portalDialogHelper;

    /**
     * 日志前缀
     */
    private static final String LOG_PREFIX = "[AidaAnalysisSessionListener]";

    /**
     * task异常退出类型
     */
    private static final String TASK_EXCEPTION_END = "abnormalExit";

    @MdpMafkaMsgReceive
    public ConsumeStatus receive(String msgBody) {
        Transaction transaction = Cat.newTransaction(CatConstants.ONLINE_SESSION_MQ_TYPE, CatConstants.ONLINE_SESSION_DATA_SYNC);
        try {
            AidaAnalysisSessionDTO sessionDTO = JSON.parseObject(msgBody, AidaAnalysisSessionDTO.class);
            if (null == sessionDTO) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            // 目前全都是在线 不需要判断渠道
            String sessionId = sessionDTO.getSessionId();
            AidaOnlineSessionCacheDTO sessionCacheDTO = redisService.getSessionInfo(sessionId);
            if (sessionCacheDTO == null) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            // 补全session信息
            AidaOnlineSessionEsDTO sessionEsDTO = null;
            if (AnalysisMessageTypeEnum.DATA.getCode().equals(sessionDTO.getType())) {
                sessionEsDTO = completeSession(sessionId, sessionCacheDTO);
            } else if (AnalysisMessageTypeEnum.DIALOG.getCode().equals(sessionDTO.getType())) {
                sessionEsDTO = completeDialog(sessionId);
            }
            if (null == sessionEsDTO) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            // 尝试获取锁
            if (!redisService.getSessionLock(sessionId)) {
                log.error("{}-获取锁失败, sessionId = {}, sessionCacheDTO = {}", LOG_PREFIX, sessionId, JSON.toJSONString(sessionCacheDTO));
                return ConsumeStatus.CONSUME_FAILURE;
            }
            try {
                // 保存到ES
                eagleService.upsert(sessionCacheDTO.getSessionIndex(), sessionId, sessionEsDTO);
            } catch (Exception e) {
                log.error("{}-保存门户消息信息异常, sessionId = {}, e:", LOG_PREFIX, sessionId, e);
                transaction.setStatus(e);
            } finally {
                redisService.deleteSessionLock(sessionId);
            }
        }  catch (Exception e) {
            log.error("{}-消费失败, msgBody = {}", LOG_PREFIX, msgBody, e);
            transaction.setStatus(e);
        } finally {
            transaction.complete();
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    /**
     * 补全session数据
     *
     * @param sessionId       会话ID
     * @param sessionCacheDTO 会话缓存
     * @return 会话ES
     */
    private AidaOnlineSessionEsDTO completeSession(String sessionId, AidaOnlineSessionCacheDTO sessionCacheDTO) {
        // 查询会话分析es
        AidaOnlineSessionEsDTO sessionEsDTO = getSessionById(sessionId, sessionCacheDTO.getSessionIndex());
        if (null == sessionEsDTO) {
            log.error("{}-查询会话分析ES为空, sessionId = {}", LOG_PREFIX, sessionId);
            return null;
        }
        // 补全会话数据
        completeSessionData(sessionEsDTO);
        completeRobotInvokeLog(sessionEsDTO);
        completeTaskInfo(sessionEsDTO, sessionCacheDTO.getInstanceIdList());
        return sessionEsDTO;
    }

    /**
     * 补全对话数据
     *
     * @param sessionId 会话ID
     * @return 会话ES
     */
    private AidaOnlineSessionEsDTO completeDialog(String sessionId) {
        List<AidaOnlineSessionEsDTO.DialogMessage> messageList = portalDialogHelper.listHandleMessage(sessionId);
        if (CollectionUtils.isEmpty(messageList)) {
            return null;
        }
        AidaOnlineSessionEsDTO sessionEsDTO = new AidaOnlineSessionEsDTO();
        sessionEsDTO.setMessages(messageList);
        return sessionEsDTO;
    }

    /**
     * 补充TASK信息
     *
     * @param sessionDTO         会话文档
     * @param taskInstanceIdList 实例列表
     */
    private void completeTaskInfo(AidaOnlineSessionEsDTO sessionDTO, List<String> taskInstanceIdList) {
        if (CollectionUtils.isEmpty(taskInstanceIdList)) {
            return;
        }
        // 获取已保存的task记录
        List<AidaOnlineSessionEsDTO.TaskTraceLog> existingTraceLogs = Optional.ofNullable(sessionDTO.getTaskTraceLogs()).orElse(Lists.newArrayList());
        for (String taskInstanceId : taskInstanceIdList) {
            // 查询扶摇补全task信息
            List<TaskLogSearchResult> taskLogSearchResultList = portalEagleServiceProxy.getTaskInfoFromPortalRobot(taskInstanceId);
            if (CollectionUtils.isEmpty(taskLogSearchResultList)) {
                continue;
            }
            for (TaskLogSearchResult taskLogSearchResult : taskLogSearchResultList) {
                // 查找已保存的task记录中是否已存在该task
                AidaOnlineSessionEsDTO.TaskTraceLog taskTraceLog = existingTraceLogs.stream()
                        .filter(e -> Objects.equals(e.getTaskProcessInstanceId(), taskInstanceId)
                                && Objects.equals(e.getTaskName(), taskLogSearchResult.getTaskName())
                                && Objects.equals(e.getTaskKey(), taskLogSearchResult.getTaskKey())
                                && Objects.equals(e.getTaskVersion(), taskLogSearchResult.getTaskVersion()))
                        .findFirst()
                        .orElse(null);
                // 不存在新增一个
                if (null == taskTraceLog) {
                    taskTraceLog = new AidaOnlineSessionEsDTO.TaskTraceLog();
                    taskTraceLog.setTaskProcessInstanceId(taskInstanceId);
                    taskTraceLog.setTaskVersion(taskLogSearchResult.getTaskVersion());
                    taskTraceLog.setTaskName(taskLogSearchResult.getTaskName());
                    taskTraceLog.setTaskKey(taskLogSearchResult.getTaskKey());
                    taskTraceLog.setTaskNodeNames(Lists.newArrayList(taskLogSearchResult.getNodeName()));
                    taskTraceLog.setTaskNodeIds(Lists.newArrayList(taskLogSearchResult.getNodeId()));
                    existingTraceLogs.add(taskTraceLog);
                } else {
                    // 存在更新taskNode和taskName
                    List<String> taskNodeIds = Optional.ofNullable(taskTraceLog.getTaskNodeIds()).orElse(Lists.newArrayList());
                    if (StringUtils.isNotBlank(taskLogSearchResult.getNodeId()) && !taskNodeIds.contains(taskLogSearchResult.getNodeId())) {
                        taskNodeIds.add(taskLogSearchResult.getNodeId());
                    }
                    taskTraceLog.setTaskNodeIds(taskNodeIds);
                    List<String> taskNodeNames = Optional.ofNullable(taskTraceLog.getTaskNodeNames()).orElse(Lists.newArrayList());
                    if (StringUtils.isNotBlank(taskLogSearchResult.getNodeName()) && !taskNodeNames.contains(taskLogSearchResult.getNodeName())) {
                        taskNodeNames.add(taskLogSearchResult.getNodeName());
                    }
                    taskTraceLog.setTaskNodeNames(taskNodeNames);
                }
            }
        }
        sessionDTO.setTaskTraceLogs(existingTraceLogs);
    }

    /**
     * 补充会话数据
     *
     * @param sessionDTO 会话文档
     */
    private void completeSessionData(AidaOnlineSessionEsDTO sessionDTO) {
        SessionSearchResult sessionSearchResult = portalEagleServiceProxy.getSessionInfoFromPortal(sessionDTO.getSessionId());
        if (null == sessionSearchResult) {
            return;
        }
        sessionDTO.setOrderIdList(addAbsentElements(sessionDTO.getOrderIdList(), sessionSearchResult.getOrderIdList()));
        sessionDTO.setVisitIdList(addAbsentElements(sessionDTO.getVisitIdList(), sessionSearchResult.getVisitIdList()));
        sessionDTO.setTypeList(addAbsentElements(sessionDTO.getTypeList(), sessionSearchResult.getTypeList()));
        sessionDTO.setSenderTypeList(addAbsentElements(sessionDTO.getSenderTypeList(), sessionSearchResult.getSenderTypeList()));
        sessionDTO.setTypicalQuestionIdList(addAbsentElements(sessionDTO.getTypicalQuestionIdList(), sessionSearchResult.getKnowledgeIdList()));
        if (StringUtils.isNotBlank(sessionSearchResult.getInitOrderId())) {
            sessionDTO.setOrderId(sessionSearchResult.getInitOrderId());
        }
        if (StringUtils.isBlank(sessionDTO.getStars()) && StringUtils.isNotBlank(sessionSearchResult.getNpsStarNum())) {
            sessionDTO.setStars(sessionSearchResult.getNpsStarNum());
        }
        if (StringUtils.isNotBlank(sessionSearchResult.getNpsAnswerValue()) && StringUtils.isBlank(sessionDTO.getSessionSolved())) {
            sessionDTO.setSessionSolved(QuestionSolveEnum.getCodeByDesc(sessionSearchResult.getNpsAnswerValue()));
        }
        if (StringUtils.isBlank(sessionDTO.getIsTimeoutEnd()) && Boolean.TRUE.toString().equals(sessionSearchResult.getIsTimeoutEnd())) {
            sessionDTO.setIsTimeoutEnd(Boolean.TRUE.toString());
        }
        if (StringUtils.isBlank(sessionDTO.getIsExceptionEnd())
                && CollectionUtils.isNotEmpty(sessionSearchResult.getTaskExceptionTypes())
                && sessionSearchResult.getTaskExceptionTypes().contains(TASK_EXCEPTION_END)) {
            sessionDTO.setIsExceptionEnd(Boolean.TRUE.toString());
        }
    }

    /**
     * 补充标问信息
     *
     * @param sessionDTO 会话文档
     */
    private void completeRobotInvokeLog(AidaOnlineSessionEsDTO sessionDTO) {
        RobotInvokeLogSearchResult robotInvokeLogSearchResult = portalEagleServiceProxy.getRobotInfoFromPortalRobot(sessionDTO.getSessionId());
        if (null == robotInvokeLogSearchResult || CollectionUtils.isEmpty(robotInvokeLogSearchResult.getRobotInvokeLogList())) {
            return;
        }
        // 获取现有的robot invoke logs
        List<AidaOnlineSessionEsDTO.RobotInvokeLog> existingRobotLogs = Optional.ofNullable(sessionDTO.getRobotInvokeLogs()).orElse(Lists.newArrayList());

        // 转换并合并新的robot logs
        List<RobotInvokeLog> robotInvokeLogList = robotInvokeLogSearchResult.getRobotInvokeLogList();
        for (RobotInvokeLog robotLog : robotInvokeLogList) {
            AidaOnlineSessionEsDTO.RobotInvokeLog aidaRobotLog = convertToRobotLog(robotLog);
            // 检查是否已存在相同的robot log
            boolean exists = false;
            for (AidaOnlineSessionEsDTO.RobotInvokeLog existingLog : existingRobotLogs) {
                if (Objects.equals(existingLog.getTriggerTime(), aidaRobotLog.getTriggerTime())
                        && Objects.equals(existingLog.getTriggerName(), aidaRobotLog.getTriggerName())
                        && Objects.equals(existingLog.getUserQuery(), aidaRobotLog.getUserQuery())
                        && Objects.equals(existingLog.getTypicalQuestionId(), aidaRobotLog.getTypicalQuestionId())
                        && Objects.equals(existingLog.getTypicalQuestionName(), aidaRobotLog.getTypicalQuestionName())) {
                    exists = true;
                    break;
                }
            }
            if (!exists) {
                existingRobotLogs.add(aidaRobotLog);
            }
        }
        sessionDTO.setRobotInvokeLogs(existingRobotLogs);
    }

    /**
     * 标问对象转换
     *
     * @param robotInvokeLog 扶摇ES标问对象
     * @return AI搭ES标问对象
     */
    private AidaOnlineSessionEsDTO.RobotInvokeLog convertToRobotLog(RobotInvokeLog robotInvokeLog) {
        AidaOnlineSessionEsDTO.RobotInvokeLog aidaRobotInvokeLog = new AidaOnlineSessionEsDTO.RobotInvokeLog();
        if (StringUtils.isNotBlank(robotInvokeLog.getTriggerTime())) {
            aidaRobotInvokeLog.setTriggerTime(new Date(Long.parseLong(robotInvokeLog.getTriggerTime())));
        }
        aidaRobotInvokeLog.setTriggerName(robotInvokeLog.getTriggerName());
        aidaRobotInvokeLog.setTypicalQuestionId(robotInvokeLog.getTypicalQuestionId());
        aidaRobotInvokeLog.setTypicalQuestionName(robotInvokeLog.getTypicalQuestionName());
        aidaRobotInvokeLog.setUserQuery(robotInvokeLog.getUserQuery());
        return aidaRobotInvokeLog;
    }

    /**
     * 根据会话ID获取会话es文档
     *
     * @param sessionId 会话ID
     * @param index     索引
     * @return 会话文档
     */
    private AidaOnlineSessionEsDTO getSessionById(String sessionId, String index) {
        GetRequest request = new GetRequest(index, sessionId);

        String source = eagleService.get(request);
        if (StringUtils.isBlank(source)) {
            log.error("{}-getSessionById fail sessionId = {}, index = {}", LOG_PREFIX, sessionId, index);
            return null;
        }
        return JSON.parseObject(source, AidaOnlineSessionEsDTO.class);
    }

    /**
     * 补充缺失的数据
     *
     * @param aidaList   AI搭字段list
     * @param portalList 扶摇字段list
     * @return 是否有缺失
     */
    private List<String> addAbsentElements(List<String> aidaList, List<String> portalList) {
        if (null == aidaList) {
            aidaList = Lists.newArrayList();
        }
        if (CollectionUtils.isEmpty(portalList)) {
            return aidaList;
        }
        for (String item : portalList) {
            if (!aidaList.contains(item)) {
                aidaList.add(item);
            }
        }
        return aidaList;
    }

}
