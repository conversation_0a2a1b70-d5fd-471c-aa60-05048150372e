package com.meituan.csc.aigc.eval.mq.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.csc.nps.biz.api.base.dto.RespDTO;
import com.dianping.csc.nps.biz.api.questionnaire.dto.AggregationAnswerDTO;
import com.dianping.csc.nps.biz.api.questionnaire.dto.AggregationAnswerSubDTO;
import com.dianping.csc.nps.biz.api.questionnaire.service.QuestionnaireSatisfactionRemoteService;
import com.google.common.collect.Maps;
import com.meituan.csc.aigc.eval.constants.CatConstants;
import com.meituan.csc.aigc.eval.constants.EsFieldConstants;
import com.meituan.csc.aigc.eval.constants.EsScriptConstants;
import com.meituan.csc.aigc.eval.dto.es.AidaOnlineSessionEsDTO;
import com.meituan.csc.aigc.eval.dto.mq.NpsCore;
import com.meituan.csc.aigc.eval.dto.workbench.DtsDTO;
import com.meituan.csc.aigc.eval.enums.DtsMessageTypeEnum;
import com.meituan.csc.aigc.eval.enums.es.online.QuestionSolveEnum;
import com.meituan.csc.aigc.eval.service.analysis.OnlineSessionRedisService;
import com.meituan.csc.aigc.eval.service.analysis.es.CaseAnalysisEagleService;
import com.meituan.csc.aigc.eval.utils.EagleUtil;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mdp.boot.starter.mafka.consumer.anno.MdpMafkaMsgReceive;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 问卷MQ消息消费服务，用于更新在线会话ES
 *
 * <AUTHOR>
 * @date 2025/5/6
 */
@Slf4j
@Service("npsCoreListener")
public class NpsCoreListener {

    @Autowired
    private OnlineSessionRedisService redisService;

    @Autowired
    private CaseAnalysisEagleService eagleService;

    @Autowired
    private QuestionnaireSatisfactionRemoteService questionnaireSatisfactionRemoteService;

    /**
     * 日志前缀
     */
    private static final String LOG_PREFIX = "[NpsCoreListener]";

    /**
     * 索引前缀
     */
    private static final String SESSION_INDEX = "aida_session_online_";

    /**
     * 已评价状态
     */
    private static final int REVIEWED_EVALUATION_STATUS = 1;

    private static final Integer TYPE_2 = 2;

    @MdpMafkaMsgReceive
    public ConsumeStatus receive(String msgBody) {
        Transaction transaction = Cat.newTransaction(CatConstants.ONLINE_SESSION_MQ_TYPE, CatConstants.ONLINE_SESSION_NPS);
        try {
            DtsDTO<NpsCore> dtBusMsgDto = JSON.parseObject(msgBody, new TypeReference<DtsDTO<NpsCore>>() {});
            // 判断是否可以消费
            if (!DtsMessageTypeEnum.INSERT.getType().equals(dtBusMsgDto.getType()) && !DtsMessageTypeEnum.UPDATE.getType().equals(dtBusMsgDto.getType())) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            NpsCore npsCore = dtBusMsgDto.getData();
            if (npsCore.getEvaluationStatus() != REVIEWED_EVALUATION_STATUS || !NpsCore.NpsTypeEnum.getTypes().contains(npsCore.getNpsOriginalType().intValue())) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            RespDTO<AggregationAnswerDTO> respDTO = questionnaireSatisfactionRemoteService.findAggregationAnswerByNpsCoreId(npsCore.getId());
            if (null == respDTO || !respDTO.isSuccess()) {
                log.error("{}-查询问卷信息失败, id = {}, respDTO = {}", LOG_PREFIX, npsCore.getId(), JSON.toJSONString(respDTO));
                return ConsumeStatus.RECONSUME_LATER;
            }
            if (null == respDTO.getData() || StringUtils.isBlank(respDTO.getData().getBizId())) {
                return ConsumeStatus.RECONSUME_LATER;
            }
            AggregationAnswerDTO answerDTO = respDTO.getData();

            List<AidaOnlineSessionEsDTO> sessionList = getSessionsByVisitId(answerDTO.getBizId());
            if (CollectionUtils.isEmpty(sessionList)) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            // 1. 构建更新参数
            Map<String, Object> map = getParamMap(answerDTO);
            if (MapUtils.isEmpty(map)) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            // 2. 操作更新
            for (AidaOnlineSessionEsDTO esDTO : sessionList) {
                String sessionId = esDTO.getSessionId();
                if (!redisService.getSessionLock(sessionId)) {
                    log.error("{}-获取锁失败, sessionId = {}, map = {}", LOG_PREFIX, sessionId, JSON.toJSONString(map));
                    return ConsumeStatus.CONSUME_FAILURE;
                }
                try {
                    eagleService.upsert(EagleUtil.getIndexByDate(SESSION_INDEX, esDTO.getAddTime()), sessionId, map);
                } catch (Exception e) {
                    log.error("{}-更新会话信息异常, sessionId = {}, e:", LOG_PREFIX, sessionId, e);
                    transaction.setStatus(e);
                } finally {
                    redisService.deleteSessionLock(sessionId);
                }
            }
        } catch (Exception e) {
            log.error("{}-消费失败, msgBody = {}", LOG_PREFIX, msgBody, e);
            transaction.setStatus(e);
        } finally {
            transaction.complete();
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    /**
     * 根据visitId查询相关session
     *
     * @param visitId 访问ID
     * @return 会话列表
     */
    private List<AidaOnlineSessionEsDTO> getSessionsByVisitId(String visitId) {
        SearchRequest request = new SearchRequest(EagleUtil.getIndices(null, null, SESSION_INDEX));

        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termQuery(EsFieldConstants.VISIT_ID_LIST, visitId));
        sourceBuilder.query(boolQuery);
        sourceBuilder.fetchSource(new String[]{EsFieldConstants.SESSION_ID, EsFieldConstants.ADD_TIME}, null);

        request.source(sourceBuilder);
        SearchHits searchHits = eagleService.search(request);
        if (null == searchHits) {
            log.error("{}-getSessionById fail, visitId = {}", LOG_PREFIX, visitId);
            return null;
        }
        if (0 == searchHits.getHits().length) {
            return null;
        }
        List<AidaOnlineSessionEsDTO> sessionList = Lists.newArrayList();
        for (SearchHit hit : searchHits.getHits()) {
            AidaOnlineSessionEsDTO esDTO = JSON.parseObject(hit.getSourceAsString(), AidaOnlineSessionEsDTO.class);
            sessionList.add(esDTO);
        }
        return sessionList;
    }

    /**
     * 获取问卷参数Map
     *
     * @param answerDTO 问卷信息
     * @return 问卷参数Map
     */
    private Map<String, Object> getParamMap(AggregationAnswerDTO answerDTO) {
        Map<String, Object> map = Maps.newHashMap();
        // 问卷评分 是否解决
        String starNum = null;
        String answerValue = null;
        for (AggregationAnswerSubDTO answerSubDTO : answerDTO.getAnswerList()) {
            if (answerSubDTO.getStarNum() != null) {
                starNum = String.valueOf(answerSubDTO.getStarNum());
            }
            if ((TYPE_2.equals(answerSubDTO.getQuestionType()) || answerSubDTO.getStarNum() == null) && !StringUtils.isEmpty(answerSubDTO.getAnswerValue())) {
                answerValue = QuestionSolveEnum.getCodeByDesc(answerSubDTO.getAnswerValue());
            }
        }
        if (StringUtils.isNotBlank(starNum)) {
            map.put(EsScriptConstants.STAR_PARAM, starNum);
        }
        if (StringUtils.isNotBlank(answerValue)) {
            map.put(EsScriptConstants.SESSION_SOLVED_PARAM, answerValue);
        }
        return map;
    }
}
