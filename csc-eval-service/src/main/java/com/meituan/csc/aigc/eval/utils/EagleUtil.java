package com.meituan.csc.aigc.eval.utils;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Date;

/**
 * ES工具类
 *
 * <AUTHOR>
 * @date 2025/5/7
 */
public class EagleUtil {

    /**
     * 获取索引列表
     *
     * @param beginTime   开始时间
     * @param endTime     结束时间
     * @param indexPrefix 索引前缀
     * @return 索引列表
     */
    public static String[] getIndices(Long beginTime, Long endTime, String indexPrefix) {
        if (indexPrefix == null) {
            return new String[]{};
        }

        // 获取5个月前的时间作为最小时间
        LocalDate now = LocalDate.now();
        LocalDate minDate = now.minusMonths(5);

        // 转换为LocalDate
        LocalDate beginDate = beginTime == null ? minDate : Instant.ofEpochMilli(beginTime).atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate endDate = endTime == null ? now : Instant.ofEpochMilli(endTime).atZone(ZoneId.systemDefault()).toLocalDate();


        // 兜底
        if (beginDate.isBefore(LocalDate.of(2025,5,1))) {
            beginDate = LocalDate.of(2025, 5, 1);
        }

        // 限制时间范围
        if (beginDate.isBefore(minDate)) beginDate = minDate;
        if (endDate.isBefore(minDate)) endDate = minDate;
        if (beginDate.isAfter(now)) beginDate = now;
        if (endDate.isAfter(now)) endDate = now;

        // 统一到月初
        beginDate = beginDate.withDayOfMonth(1);
        endDate = endDate.withDayOfMonth(1);

        // 保证beginDate <= endDate
        if (beginDate.isAfter(endDate)) {
            LocalDate temp = beginDate;
            beginDate = endDate;
            endDate = temp;
        }
        // 计算月份差
        long months = ChronoUnit.MONTHS.between(beginDate, endDate);
        int size = (int) months + 1;
        // 生成索引列表
        String[] indices = new String[size];
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        for (int i = 0; i < size; i++) {
            LocalDate currentDate = beginDate.plusMonths(i);
            indices[i] = indexPrefix + currentDate.format(formatter);
        }
        return indices;
    }

    /**
     * 按日期获取索引
     *
     * @param indexPrefix 索引前缀
     * @param date        日期
     * @return 索引
     */
    public static String getIndexByDate(String indexPrefix, Date date) {
        return indexPrefix + new SimpleDateFormat("yyyy-MM").format(date);
    }

}
