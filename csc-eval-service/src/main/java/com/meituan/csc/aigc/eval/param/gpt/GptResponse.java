package com.meituan.csc.aigc.eval.param.gpt;

import com.meituan.csc.aigc.runtime.common.ResultCodeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class GptResponse implements Serializable {

    /**
     * gpt接口状态码
     */
    private Integer gptStatus;

    /**
     * gpt接口提示信息
     */
    private String gptMessage;

    /**
     * gpt回复内容
     */
    private String gptReply;

    /**
     * gpt请求内容
     */
    private String gptContent;

    /**
     * 花费时间
     */
    private Long costTime;


    /**
     * 执行结果信息
     */
    private ResultCodeEnum resultCodeEnum;

    /**
     * 总体token数
     */
    private Long totalToken;

    /**
     * completion的token数
     */
    private Long completionToken;

    /**
     * prompt的token数
     */
    private Long promptToken;

    /**
     * 异常信息
     */
    private String errorMsg;

    private String context;
}
