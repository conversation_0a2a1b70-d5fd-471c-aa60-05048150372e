package com.meituan.csc.aigc.eval.dto.es;

import lombok.Data;

import java.util.List;

/**
 * 会话查询参数DTO
 *
 * <AUTHOR>
 * @date 2025/5/7
 */
@Data
public class OnlineSessionSearchDTO {

    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 页大小
     */
    private Integer pageSize;
    
    /**
     * 会话ID
     */
    private List<String> sessionIdList;
    /**
     * 场景
     */
    private String scene;
    /**
     * 空间ID
     */
    private String spaceId;
    /**
     * 应用ID
     */
    private String appId;
    /**
     * 版本Id
     */
    private String versionId;
    
    /**
     * 开始时间
     */
    private Long startTime;
    
    /**
     * 结束时间
     */
    private Long endTime;

    /**
     * 业务线ID
     */
    private String bu;

    /**
     * 子业务线ID
     */
    private String subBu;
    
    /**
     * 是否超时结束
     */
    private Boolean isTimeoutEnd;
    
    /**
     * 是否异常结束
     */
    private Boolean isExceptionEnd;
    
    /**
     * 访问ID
     */
    private String visitId;
    
    /**
     * 标准问
     */
    private String questionName;

    /**
     * 用户ID
     */
    private String userId;
    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 问题是否解决
     * @see com.meituan.csc.aigc.eval.enums.es.online.QuestionSolveEnum
     */
    private String sessionSolved;
    
    /**
     * 满意度
     * @see com.meituan.csc.aigc.eval.enums.es.online.EvaluationStarEnum
     */
    private List<String> stars;
    
    /**
     * 转人工状态列表
     * @see com.meituan.csc.aigc.eval.enums.es.online.TransferStaffEnum
     */
    private List<String> transferStaff;

    /**
     * taskKey
     */
    private String taskKey;
    /**
     * task版本
     */
    private String taskVersion;
    /**
     * task节点ID
     */
    private String taskNodeId;

    /**
     * 用户query
     */
    private String queryContent;
    /**
     * 回复内容
     */
    private String responseContent;
}