package com.meituan.csc.aigc.eval.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.dianping.cat.Cat;
import com.meituan.csc.aigc.eval.exception.EvalException;
import com.meituan.csc.aigc.eval.service.UploadPlatformService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.poi.ss.SpreadsheetVersion;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class ExcelService {

    @Autowired
    private UploadPlatformService uploadPlatformService;

    @PostConstruct
    public void init() {
        SpreadsheetVersion excel = SpreadsheetVersion.EXCEL2007;
        if (Integer.MAX_VALUE != excel.getMaxTextLength()) {
            Field field;
            try {
                field = excel.getClass().getDeclaredField("_maxTextLength");
                field.setAccessible(true);
                field.set(excel, Integer.MAX_VALUE);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public ByteArrayInputStream generateExcelStreamWithMultiHead(List<List<String>> headList, List<List<String>> data) {
        return generateExcelStreamWithMultiHead(headList, data, "Sheet1");
    }

    public ByteArrayInputStream generateExcelStreamWithMultiHead(List<List<String>> headList, List<List<String>> data, String sheetName) {
        List<ExcelData> excelDataList = new ArrayList<>();
        ExcelData excelData = new ExcelData();
        excelData.setHeadList(headList);
        excelData.setData(data);
        excelData.setSheetName(sheetName);
        excelDataList.add(excelData);
        return generateExcelStreamWithMultiHead(excelDataList);
    }

    public ByteArrayInputStream generateExcelStreamWithMultiHead(List<ExcelData> excelDataList) {
        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
             ExcelWriter excelWriter = EasyExcelFactory.write(byteArrayOutputStream).build()) {
            for (ExcelData excelData : excelDataList) {
                // 创建新的Sheet，并设置sheetName
                WriteSheet writeSheet = EasyExcelFactory.writerSheet().head(excelData.getHeadList()).sheetName(excelData.getSheetName()).build();
                // 将headList和data写入当前的Sheet
                excelWriter.write(excelData.getData(), writeSheet);
            }
            // 将所有的sheet写入到byteArrayOutputStream中
            excelWriter.finish();
            // 上传 Excel 文件到 S3
            return new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
        } catch (Exception e) {
            Cat.logError(e);
            throw new EvalException("生成Excel文件上传失败");
        }
    }
    public ByteArrayInputStream generateExcelStreamWithMultiHeadManual(List<ExcelData> excelDataList) {
        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
             ExcelWriter excelWriter = EasyExcelFactory.write(byteArrayOutputStream).build()) {
            for (ExcelData excelData : excelDataList) {
                // 创建新的Sheet，并设置sheetName
                WriteSheet writeSheet = EasyExcelFactory.writerSheet().head(excelData.getHeadList()).sheetName(excelData.getSheetName()).build();
                // 将headList和data写入当前的Sheet
                excelWriter.write(excelData.getData(), writeSheet);
            }
            // 将所有的sheet写入到byteArrayOutputStream中
            excelWriter.finish();
            // 上传 Excel 文件到 S3
            return new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
        } catch (Exception e) {
            Cat.logError(e);
            throw new EvalException("生成Excel文件上传失败");
        }
    }

    /**
     *以queryId未分割点合并queryId相同的行单元格
     * @param excelDataList
     * @return
     */
    public ByteArrayInputStream generateExcelMergeRow(List<ExcelData> excelDataList) {
        ExcelData excelData = excelDataList.get(0);
        List<List<String>> headList = excelData.getHeadList();
        List<List<String>> data = excelData.getData();
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        ExcelWriter excelWriter = EasyExcelFactory.write(byteArrayOutputStream).build();

        int queryIdColumnIndex = -1;
        for (int i = 0; i < headList.size(); i++) {
            if ("queryId".equals(headList.get(i).get(0))) {
                queryIdColumnIndex = i-1;
                break;
            }
        }

        if (queryIdColumnIndex == -1) {
            throw new EvalException("未找到 queryId 列，请检查表头配置");
        }

        // 创建新的Sheet
        WriteSheet writeSheet = EasyExcelFactory.writerSheet().head(headList).sheetName("Sheet1").build();
        // 写入数据以初始化上下文
        excelWriter.write(Collections.emptyList(), writeSheet);

        // 遍历数据，处理跨行合并
        int startRow = 0; // 合并起始行
        for (int rowIndex = 1; rowIndex < data.size(); rowIndex++) {
            List<String> nextRow = data.get(rowIndex).stream().filter(Objects::nonNull).collect(Collectors.toList());
            List<String> currentRow = data.get(rowIndex - 1).stream().filter(Objects::nonNull).collect(Collectors.toList());


            // 如果 queryId 列的值不同，或者到达最后一行，则合并前面的行
            if (!currentRow.get(queryIdColumnIndex).equals(nextRow.get(queryIdColumnIndex)) || rowIndex == data.size() - 1) {
                int endRow = (rowIndex == data.size() - 1 && currentRow.get(queryIdColumnIndex).equals(nextRow.get(queryIdColumnIndex))) ? rowIndex : rowIndex - 1;

                // 合并 queryId 列及其前面的所有列
                for (int colIndex = 0; colIndex <= queryIdColumnIndex; colIndex++) {
                    addMergedRegionIfValid(
                            excelWriter.writeContext().writeSheetHolder().getSheet(),
                            startRow + 1, endRow + 1, colIndex, colIndex
                    );
                }
                startRow = rowIndex; // 更新起始行
            }
        }

        // 将headList和data写入当前的Sheet
        excelWriter.write(data, writeSheet);

        // 将所有的sheet写入到byteArrayOutputStream中
        excelWriter.finish();
        return new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
    }


    private void addMergedRegionIfValid(Sheet sheet, int startRow, int endRow, int startCol, int endCol) {
        // 检查合并区域是否有效
        if (startRow < endRow || startCol < endCol) {
            CellRangeAddress newRegion = new CellRangeAddress(startRow, endRow, startCol, endCol);
            for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
                CellRangeAddress existingRegion = sheet.getMergedRegion(i);
                if (existingRegion.intersects(newRegion)) {
                    return; // 跳过重叠区域
                }
            }
            sheet.addMergedRegion(newRegion);
        }
    }


    public ByteArrayInputStream generateExcelStream(List<String> headList, List<List<String>> data) {
        List<List<String>> newHeadList = new ArrayList<>();
        for (String head : headList) {
            List<String> newHead = new ArrayList<>();
            newHead.add(head);
            newHeadList.add(newHead);
        }
        return generateExcelStreamWithMultiHead(newHeadList, data);
    }

    public <T> String generateSingletonHeadExcelAndUpload(List<T> dataList, Class clazz, String s3FileName, String encryptionFileName, String fileType, String staffMis) {
        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
            // 将 chatModels 写入 Excel 文件
            EasyExcelFactory.write(byteArrayOutputStream, clazz).sheet(0).doWrite(dataList);
            // 上传 Excel 文件到 S3
            ByteArrayInputStream inputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
            return uploadPlatformService.uploadS3AndEncryption(inputStream, s3FileName, encryptionFileName, fileType, staffMis);
        } catch (Exception e) {
            Cat.logError(e);
            throw new EvalException("生成Excel文件上传失败");
        }
    }

    public String generateMultiHeadExcelAndUpload(List<List<String>> headList, List<List<String>> data, String s3FileName, String encryptionFileName, String fileType, String staffMis) {
        ByteArrayInputStream inputStream = generateExcelStreamWithMultiHead(headList, data);
        return uploadPlatformService.uploadS3AndEncryption(inputStream, s3FileName, encryptionFileName, fileType, staffMis);
    }

    public String generateSingletonHeadExcelAndUpload(List<String> headList, List<List<String>> data, String s3FileName, String encryptionFileName, String fileType, String staffMis) {
        ByteArrayInputStream inputStream = generateExcelStream(headList, data);
        return uploadPlatformService.uploadS3AndEncryption(inputStream, s3FileName, encryptionFileName, fileType, staffMis);
    }

    public String generateSingletonColorfulHeadExcelAndUpload(List<ColorfulHead> headList, List<List<String>> data, String sheetName, String s3FileName, String encryptionFileName, String fileType, String staffMis) {
        ByteArrayInputStream inputStream = generateExcelStreamWithColorfulHead(headList, data, sheetName);
        return uploadPlatformService.uploadS3AndEncryption(inputStream, s3FileName, encryptionFileName, fileType, staffMis);
    }

    public ByteArrayInputStream generateExcelStreamWithColorfulHead(List<ColorfulHead> headList, List<List<String>> data, String sheetName) {
        List<List<String>> head = headList.stream()
                .map(header -> Collections.singletonList(header.getHeadName()))
                .collect(Collectors.toList());
        // 写入Excel到字节流
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            EasyExcel.write(outputStream)
                    .head(head)
                    .registerWriteHandler(new CellWriteHandler() {
                        @Override
                        public void afterCellDispose(CellWriteHandlerContext context) {
                            if (BooleanUtils.isTrue(context.getHead())) {
                                int columnIndex = context.getCell().getColumnIndex();
                                if (columnIndex < headList.size()) {
                                    ColorfulHead colorfulHead = headList.get(columnIndex);
                                    if (colorfulHead != null) {
                                        WriteCellData<?> cellData = context.getFirstCellData();
                                        WriteCellStyle writeCellStyle = cellData.getOrCreateStyle();

                                        // 设置字体颜色
                                        if (colorfulHead.getTextColor() != null) {
                                            WriteFont writeFont = new WriteFont();
                                            if (writeCellStyle.getWriteFont() != null) {
                                                BeanUtils.copyProperties(writeCellStyle.getWriteFont(), writeFont);
                                            }
                                            writeFont.setColor(colorfulHead.getTextColor().getIndex());
                                            writeCellStyle.setWriteFont(writeFont);
                                        }

                                        // 设置背景颜色
                                        if (colorfulHead.getBackgroundColor() != null) {
                                            writeCellStyle.setFillForegroundColor(colorfulHead.getBackgroundColor().getIndex());
                                        }

                                        cellData.setWriteCellStyle(writeCellStyle);
                                    }
                                }
                            }
                        }
                    })
                    .sheet(sheetName)
                    .doWrite(data);
            return new ByteArrayInputStream(outputStream.toByteArray());
        } catch (Exception e) {
            Cat.logError(e);
            throw new EvalException("生成Excel文件失败");
        }
    }

    public String generateMultiHeadExcelAndUpload(List<ExcelData> excelDataList, String s3FileName, String encryptionFileName, String fileType, String staffMis) {
        ByteArrayInputStream inputStream = generateExcelStreamWithMultiHead(excelDataList);
        return uploadPlatformService.uploadS3AndEncryption(inputStream, s3FileName, encryptionFileName, fileType, staffMis);
    }

    /**
     * 合并单元格
     * @param excelDataList
     * @param s3FileName
     * @param encryptionFileName
     * @param fileType
     * @param staffMis
     * @return
     */
    public String manualAnnotationExcel(List<ExcelData> excelDataList, String s3FileName, String encryptionFileName, String fileType, String staffMis) {
        ByteArrayInputStream inputStream = generateExcelStreamWithMultiHeadManual(excelDataList);
        return uploadPlatformService.uploadS3AndEncryption(inputStream, s3FileName, encryptionFileName, fileType, staffMis);
    }
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ExcelData implements Serializable {
        private List<List<String>> headList;
        private List<List<String>> data;
        private String sheetName;
    }
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ExcelDataManual implements Serializable {
        private List<String> headList;
        private List<List<String>> data;
        private String sheetName;
    }
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode(of = "headName")
    public static class ColorfulHead {
        /**
         * 表头名称
         */
        private String headName;
        /**
         * 表头字体颜色
         */
        private IndexedColors textColor;
        /**
         * 表头背景颜色
         */
        private IndexedColors backgroundColor;

        public ColorfulHead(String headName, IndexedColors textColor) {
            this.headName = headName;
            this.textColor = textColor;
        }

    }
}
