package com.meituan.csc.aigc.eval.service.analysis.cache;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.concurrent.TimeUnit;

/**
 * 本地缓存服务
 *
 * <AUTHOR>
 * @date 2025/4/30
 */
@Service
@Slf4j
public class LocalCacheService {

    private static final String LOG_PREFIX = "[LocalCacheService]";

    /**
     * 应用ID到空间ID的本地缓存
     */
    private Cache<String, String> appIdToTenantIdCache;

    /**
     * 初始化缓存
     */
    @PostConstruct
    public void init() {
        appIdToTenantIdCache = Caffeine.newBuilder()
                .maximumSize(10000)  // 最大缓存10000个条目
                .expireAfterWrite(7, TimeUnit.DAYS)  // 7天后过期
                .build();
        log.info("{}-Local cache initialized", LOG_PREFIX);
    }

    /**
     * 获取应用ID对应的空间ID
     *
     * @param appId 应用ID
     * @return 空间ID
     */
    public String getTenantId(String appId) {
        return appIdToTenantIdCache.getIfPresent(appId);
    }

    /**
     * 设置应用ID对应的空间ID
     *
     * @param appId    应用ID
     * @param tenantId 空间ID
     */
    public void setTenantId(String appId, String tenantId) {
        if (appId != null && tenantId != null) {
            appIdToTenantIdCache.put(appId, tenantId);
        }
    }
}