package com.meituan.csc.aigc.eval.param;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class ConversationInfo implements Serializable {
    private String conversationId;
    private String message;
    private Boolean isUpdate;

    public boolean isUpdateId() {
        if (isUpdate == null) {
            return false;
        }
        return isUpdate && StringUtils.isNotBlank(conversationId);
    }
}
