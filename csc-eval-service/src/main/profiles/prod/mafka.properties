mdp.mafka.consumer[0].appkey=com.sankuai.csccratos.eval.service
mdp.mafka.consumer[0].bgNameSpace=pingtai
mdp.mafka.consumer[0].topicName=dialog.event.push
mdp.mafka.consumer[0].subscribeGroup=dialog.event.push.consumer
mdp.mafka.consumer[0].listenerId=evalMessage

mdp.mafka.consumer[1].appkey=com.sankuai.csccratos.eval.service
mdp.mafka.consumer[1].bgNameSpace=pingtai
mdp.mafka.consumer[1].topicName=workbench.aida.messages.dts
mdp.mafka.consumer[1].subscribeGroup=workbench.aida.messages.dts.consumer
mdp.mafka.consumer[1].listenerId=aidaMessages

mdp.mafka.consumer[2].bgNameSpace=xm
mdp.mafka.consumer[2].appkey=com.sankuai.csccratos.eval.service
mdp.mafka.consumer[2].subscribeGroup=aida.portal.message.consumer
mdp.mafka.consumer[2].topicName=csc.portal.message
mdp.mafka.consumer[2].listenerId=portalMessageListener

mdp.mafka.consumer[3].bgNameSpace=xm
mdp.mafka.consumer[3].appkey=com.sankuai.csccratos.eval.service
mdp.mafka.consumer[3].subscribeGroup=aida.robot.invoke.log.consumer
mdp.mafka.consumer[3].topicName=csc.solution.sessionAnalysis.robotInvokeLog
mdp.mafka.consumer[3].listenerId=robotInvokeLogListener

mdp.mafka.consumer[4].bgNameSpace=pingtai
mdp.mafka.consumer[4].appkey=com.sankuai.csccratos.eval.service
mdp.mafka.consumer[4].subscribeGroup=aida.task.log.consumer
mdp.mafka.consumer[4].topicName=nlp_dialog_dm_task_log
mdp.mafka.consumer[4].listenerId=taskTraceLogListener

mdp.mafka.consumer[5].bgNameSpace=xm
mdp.mafka.consumer[5].appkey=com.sankuai.csccratos.eval.service
mdp.mafka.consumer[5].subscribeGroup=aida.nps.core.consumer
mdp.mafka.consumer[5].topicName=csc.portal.nps.core
mdp.mafka.consumer[5].listenerId=npsCoreListener

mdp.mafka.consumer[6].bgNameSpace=xm
mdp.mafka.consumer[6].appkey=com.sankuai.csccratos.eval.service
mdp.mafka.consumer[6].subscribeGroup=aida.analysis.extra.consumer
mdp.mafka.consumer[6].topicName=csc.analtics.web.analysis.extra
mdp.mafka.consumer[6].listenerId=clientLogExtraListener

mdp.mafka.consumer[7].appkey=com.sankuai.csccratos.eval.service
mdp.mafka.consumer[7].bgNameSpace=com.sankuai.mafka.castle.mtpingtai
mdp.mafka.consumer[7].topicName=manual.labeling.results
mdp.mafka.consumer[7].subscribeGroup=auto.analysis.tool.consumer
mdp.mafka.consumer[7].listenerId=manualLabelingResults

mdp.mafka.consumer[8].appkey=com.sankuai.csccratos.eval.service
mdp.mafka.consumer[8].bgNameSpace=com.sankuai.mafka.castle.mtpingtai
mdp.mafka.consumer[8].topicName=aida.case.analysis.topic
mdp.mafka.consumer[8].subscribeGroup=aida.analysis.session.consumer
mdp.mafka.consumer[8].listenerId=aidaAnalysisSessionListener

mdp.mafka.producer[0].appkey=com.sankuai.csccratos.eval.service
mdp.mafka.producer[0].bgNameSpace=com.sankuai.mafka.castle.mtpingtai
mdp.mafka.producer[0].topicName=aida.case.analysis.topic
mdp.mafka.producer[0].producerName=caseAnalysisProducer
mdp.mafka.producer[0].delay=true